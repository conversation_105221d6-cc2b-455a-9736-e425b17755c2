# 🔐 Admin Login Credentials

## Working Login Credentials

### Primary Admin Account
- **Username:** `admin`
- **Password:** `admin`
- **Status:** ✅ Active, Staff, Superuser

### Test Admin Account  
- **Username:** `test`
- **Password:** `test`
- **Status:** ✅ Active, Staff, Superuser (newly created)

### Inactive Account
- **Username:** `testadmin`
- **Status:** ❌ Password unknown

## How to Login

1. **Go to Admin Access Portal:**
   - Visit: http://127.0.0.1:8000/admin-access/
   - Click "ADMIN LOGIN" button

2. **Or go directly to login:**
   - Visit: http://127.0.0.1:8000/admin-login/

3. **Enter credentials:**
   - Use either `admin`/`admin` or `test`/`test`
   - Click "Login to Admin Panel"

4. **After successful login:**
   - You'll be redirected to the admin dashboard
   - URL: http://127.0.0.1:8000/admin-dashboard/

## Troubleshooting

If login still doesn't work:

1. **Check browser console** for JavaScript errors
2. **Clear browser cache** and cookies
3. **Try incognito/private mode**
4. **Check if server is running** on http://127.0.0.1:8000/

## Creating New Admin Users

To create additional admin users, run:

```python
python manage.py createsuperuser
```

Or use the test script:
```bash
python test_login.py
```

## Security Notes

- These are development credentials only
- Change passwords in production
- The `test` user was created for testing purposes
- All admin users have full system access
