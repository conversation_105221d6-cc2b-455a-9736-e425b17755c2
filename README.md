# Sangguniang Bayan Ordinance System

A comprehensive web application for managing and accessing municipal ordinances, built with Django, HTMX, Alpine.js, and TailwindCSS.

## Features

### Public Features
- **Browse Ordinances**: View all approved and published ordinances
- **Advanced Search**: Search by keyword, category, year, and status
- **Responsive Design**: Mobile-friendly interface using TailwindCSS
- **PDF Export**: Download ordinances as PDF files
- **Real-time Filtering**: HTMX-powered search and filtering without page reloads

### Admin Features
- **Full CRUD Operations**: Create, read, update, and delete ordinances
- **Status Management**: Change ordinance status (Draft → Reviewed → Approved → Published)
- **Category Management**: Organize ordinances by categories
- **Sponsor Management**: Track ordinance sponsors and their positions
- **File Attachments**: Upload and manage ordinance attachments
- **Activity Logging**: Track all changes and actions
- **Admin Dashboard**: Overview of system statistics

## Technology Stack

- **Backend**: Django 4.2
- **Frontend**: TailwindCSS (CDN), HTMX, Alpine.js
- **Database**: SQLite (development) / PostgreSQL (production)
- **PDF Generation**: ReportLab
- **File Handling**: Django's built-in file handling

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sbo_system
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run migrations**
   ```bash
   python manage.py migrate
   ```

5. **Create a superuser**
   ```bash
   python manage.py createsuperuser
   ```

6. **Load sample data (optional)**
   ```bash
   python manage.py populate_sample_data
   ```

7. **Start the development server**
   ```bash
   python manage.py runserver
   ```

8. **Access the application**
   - Public interface: http://127.0.0.1:8000/
   - Admin interface: http://127.0.0.1:8000/admin/
   - Admin dashboard: http://127.0.0.1:8000/admin-dashboard/

## Usage

### For the Public

1. **Browse Ordinances**: Visit the home page and click "Browse Ordinances"
2. **Search**: Use the search bar to find specific ordinances
3. **Filter**: Use the advanced filters to narrow down results by category, year, or status
4. **View Details**: Click on any ordinance to view its full content
5. **Download PDF**: Click the "Download PDF" button on any ordinance detail page

### For Administrators

1. **Login**: Access the admin interface at `/admin/` or click "Admin Login"
2. **Dashboard**: View system statistics at `/admin-dashboard/`
3. **Manage Ordinances**: 
   - Create new ordinances through Django admin or the dashboard
   - Edit existing ordinances
   - Change status using the quick status update feature
   - Upload attachments
4. **Manage Categories**: Add and organize ordinance categories
5. **Manage Sponsors**: Add council members and their positions

## Project Structure

```
sbo_system/
├── sbo_system/           # Django project settings
├── ordinances/           # Main application
│   ├── models.py         # Database models
│   ├── views.py          # View functions
│   ├── admin.py          # Admin configuration
│   ├── urls.py           # URL routing
│   └── management/       # Management commands
├── templates/            # HTML templates
│   ├── base.html         # Base template
│   └── ordinances/       # App-specific templates
├── static/               # Static files (if any)
├── media/                # User uploads
└── requirements.txt      # Python dependencies
```

## Models

### Ordinance
- Ordinance number, title, content
- Category, year passed, status
- Sponsors (many-to-many relationship)
- Created/updated timestamps and users
- Slug for SEO-friendly URLs

### Category
- Name and slug for organizing ordinances

### Sponsor
- Name and position of council members

### Attachment
- File uploads related to ordinances
- Description and upload timestamp

### OrdinanceLog
- Activity logging for audit trail
- Tracks all changes and actions

## API Endpoints

### Public URLs
- `/` - Home page
- `/ordinances/` - Ordinance list with search/filter
- `/ordinances/<slug>/` - Ordinance detail
- `/ordinances/<slug>/pdf/` - PDF export
- `/search-suggestions/` - AJAX search suggestions

### Admin URLs
- `/admin-dashboard/` - Admin dashboard
- `/admin/ordinances/` - Admin ordinance management
- `/admin/ordinances/<id>/status/` - HTMX status update

## Customization

### Styling
The application uses TailwindCSS via CDN. To customize:
1. Modify the TailwindCSS classes in templates
2. Add custom CSS in the `<style>` section of `base.html`
3. For production, consider using a local TailwindCSS build

### Adding Features
1. Extend models in `ordinances/models.py`
2. Create/update views in `ordinances/views.py`
3. Add URL patterns in `ordinances/urls.py`
4. Create templates in `templates/ordinances/`

## Production Deployment

1. **Environment Variables**: Set up environment variables for sensitive settings
2. **Database**: Configure PostgreSQL or another production database
3. **Static Files**: Configure static file serving (WhiteNoise or CDN)
4. **Media Files**: Set up media file storage (local or cloud)
5. **Security**: Update security settings in `settings.py`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please contact the development team or create an issue in the repository.
