#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a sample banner for testing the banner system
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sbo_system.settings')
django.setup()

from django.contrib.auth import get_user_model
from ordinances.models import Banner
from django.utils import timezone

User = get_user_model()

def create_sample_banner():
    """Create a sample banner for testing"""
    
    # Get or create a superuser
    try:
        user = User.objects.filter(is_superuser=True).first()
        if not user:
            user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123'
            )
            print("Created superuser: admin/admin123")
    except Exception as e:
        print(f"Error creating user: {e}")
        return
    
    # Create sample banners
    banners_data = [
        {
            'title': 'Important Notice',
            'message': 'Public Hearing on Environmental Protection Ordinance - December 15, 2024, 9:00 AM at Municipal Hall',
            'banner_type': 'warning',
            'color_scheme': 'yellow',
            'priority': 5,
        },
        {
            'title': 'New Ordinance Published',
            'message': 'Ordinance No. 2024-001 on Traffic Management has been officially published and is now in effect.',
            'banner_type': 'success',
            'color_scheme': 'green',
            'priority': 3,
        },
        {
            'title': 'System Maintenance',
            'message': 'The ordinance portal will undergo scheduled maintenance on December 20, 2024 from 2:00 AM to 4:00 AM.',
            'banner_type': 'info',
            'color_scheme': 'blue',
            'priority': 2,
        },
        {
            'title': 'Community Announcement',
            'message': 'Join us for the Municipal Council meeting every 2nd Monday of the month at 9:00 AM.',
            'banner_type': 'announcement',
            'color_scheme': 'purple',
            'priority': 1,
        }
    ]
    
    created_count = 0
    for banner_data in banners_data:
        try:
            banner, created = Banner.objects.get_or_create(
                title=banner_data['title'],
                defaults={
                    'message': banner_data['message'],
                    'banner_type': banner_data['banner_type'],
                    'color_scheme': banner_data['color_scheme'],
                    'priority': banner_data['priority'],
                    'is_active': True,
                    'is_dismissible': True,
                    'show_icon': True,
                    'start_date': timezone.now(),
                    'created_by': user,
                    'updated_by': user,
                }
            )
            
            if created:
                created_count += 1
                print(f"Created banner: {banner.title}")
            else:
                print(f"Banner already exists: {banner.title}")
                
        except Exception as e:
            print(f"Error creating banner '{banner_data['title']}': {e}")
    
    print(f"\nSample banner creation completed!")
    print(f"Created {created_count} new banners")
    print(f"Total banners in system: {Banner.objects.count()}")
    
    # Show active banners
    active_banners = Banner.get_active_banners()
    print(f"Active banners: {active_banners.count()}")
    
    for banner in active_banners:
        print(f"  - {banner.title} ({banner.get_banner_type_display()}, Priority: {banner.priority})")

if __name__ == '__main__':
    create_sample_banner()
