from django.contrib import admin
from .models import Category, Sponsor, Ordinance, Attachment, OrdinanceLog, Official, Banner


class AttachmentInline(admin.TabularInline):
    model = Attachment
    extra = 1
    fields = ('file', 'description')


class OrdinanceLogInline(admin.TabularInline):
    model = OrdinanceLog
    extra = 0
    readonly_fields = ('user', 'action', 'timestamp', 'notes')
    can_delete = False


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'ordinance_count')
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ('name',)

    def ordinance_count(self, obj):
        return obj.ordinances.count()
    ordinance_count.short_description = 'Number of Ordinances'


@admin.register(Sponsor)
class SponsorAdmin(admin.ModelAdmin):
    list_display = ('name', 'position')
    search_fields = ('name', 'position')
    list_filter = ('position',)


@admin.register(Ordinance)
class OrdinanceAdmin(admin.ModelAdmin):
    list_display = ('ordinance_number', 'title', 'category', 'year_passed', 'status', 'created_at')
    list_filter = ('status', 'category', 'year_passed', 'created_at')
    search_fields = ('ordinance_number', 'title', 'content')
    prepopulated_fields = {'slug': ('ordinance_number', 'title')}
    filter_horizontal = ('sponsors',)
    inlines = [AttachmentInline, OrdinanceLogInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('ordinance_number', 'title', 'slug', 'category', 'year_passed')
        }),
        ('Content', {
            'fields': ('content', 'pdf_file')
        }),
        ('Status & Sponsors', {
            'fields': ('status', 'sponsors')
        }),
        ('Metadata', {
            'fields': ('created_by', 'updated_by', 'is_archived'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

        # Log the action
        action = 'Updated' if change else 'Created'
        OrdinanceLog.objects.create(
            ordinance=obj,
            user=request.user,
            action=f'{action} ordinance',
            notes=f'Ordinance {action.lower()} via admin interface'
        )


@admin.register(Attachment)
class AttachmentAdmin(admin.ModelAdmin):
    list_display = ('ordinance', 'file', 'description', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('ordinance__title', 'description')


@admin.register(OrdinanceLog)
class OrdinanceLogAdmin(admin.ModelAdmin):
    list_display = ('ordinance', 'user', 'action', 'timestamp')
    list_filter = ('action', 'timestamp')
    search_fields = ('ordinance__title', 'user__username', 'action')
    readonly_fields = ('ordinance', 'user', 'action', 'timestamp', 'notes')

    def has_add_permission(self, request):
        return False  # Logs should only be created programmatically

    def has_change_permission(self, request, obj=None):
        return False  # Logs should be immutable


@admin.register(Official)
class OfficialAdmin(admin.ModelAdmin):
    list_display = ('name', 'position', 'committee', 'status', 'term_start', 'term_end', 'order')
    list_filter = ('position', 'status', 'term_start')
    search_fields = ('name', 'committee', 'bio')
    ordering = ('order', 'position', 'name')
    list_editable = ('order', 'status')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'position', 'committee', 'profile_picture', 'order')
        }),
        ('Term Details', {
            'fields': ('term_start', 'term_end', 'status')
        }),
        ('Biography & Achievements', {
            'fields': ('bio', 'achievements'),
            'classes': ('wide',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).filter(status='active')


@admin.register(Banner)
class BannerAdmin(admin.ModelAdmin):
    list_display = ('title', 'banner_type', 'color_scheme', 'is_active', 'priority', 'start_date', 'end_date', 'created_at')
    list_filter = ('banner_type', 'color_scheme', 'is_active', 'created_at', 'start_date')
    search_fields = ('title', 'message')
    list_editable = ('is_active', 'priority')
    ordering = ('-priority', '-created_at')

    fieldsets = (
        ('Banner Content', {
            'fields': ('title', 'message', 'banner_type', 'color_scheme')
        }),
        ('Display Settings', {
            'fields': ('is_active', 'is_dismissible', 'show_icon', 'priority')
        }),
        ('Scheduling', {
            'fields': ('start_date', 'end_date'),
            'description': 'Control when this banner is displayed. Leave end date empty for permanent display.'
        }),
        ('Metadata', {
            'fields': ('created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_by', 'updated_by', 'created_at', 'updated_at')

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by', 'updated_by')

    class Media:
        css = {
            'all': ('admin/css/banner_admin.css',)
        }
        js = ('admin/js/banner_admin.js',)
