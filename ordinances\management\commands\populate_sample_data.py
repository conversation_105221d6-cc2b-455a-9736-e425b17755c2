from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from ordinances.models import Category, Sponsor, Ordinance, OrdinanceLog
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate the database with sample ordinance data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Get or create admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('No superuser found. Please create one first.'))
            return
        
        # Create categories
        categories_data = [
            'Public Safety and Order',
            'Health and Sanitation',
            'Environment and Natural Resources',
            'Traffic and Transportation',
            'Business and Commerce',
            'Education and Culture',
            'Social Services',
            'Infrastructure and Development',
            'Revenue and Taxation',
            'General Administration'
        ]
        
        categories = []
        for cat_name in categories_data:
            category, created = Category.objects.get_or_create(name=cat_name)
            categories.append(category)
            if created:
                self.stdout.write(f'Created category: {cat_name}')
        
        # Create sponsors
        sponsors_data = [
            ('<PERSON><PERSON> <PERSON>', '<PERSON>or'),
            ('<PERSON><PERSON>', 'Councilor'),
            ('<PERSON><PERSON> <PERSON>', 'Vice Mayor'),
            ('Hon. <PERSON> <PERSON>les', 'Councilor'),
            ('Hon. Carmen <PERSON>', 'Councilor'),
            ('Hon. <PERSON>', 'Councilor'),
            ('Hon. Elena Villanueva', 'Councilor'),
            ('Hon. Miguel <PERSON>', 'Mayor'),
        ]
        
        sponsors = []
        for name, position in sponsors_data:
            sponsor, created = Sponsor.objects.get_or_create(
                name=name,
                position=position
            )
            sponsors.append(sponsor)
            if created:
                self.stdout.write(f'Created sponsor: {name}')
        
        # Create sample ordinances
        ordinances_data = [
            {
                'number': 'ORD-2024-001',
                'title': 'An Ordinance Establishing Smoke-Free Zones in Public Places',
                'content': '''WHEREAS, the protection of public health is a primary concern of the local government;
WHEREAS, exposure to secondhand smoke poses serious health risks to the general public;
WHEREAS, there is a need to establish designated smoke-free zones to protect non-smokers;

NOW, THEREFORE, BE IT ORDAINED by the Sangguniang Bayan that:

Section 1. Smoke-free zones are hereby established in all public places including:
a) Government buildings and offices
b) Schools and educational institutions
c) Hospitals and health facilities
d) Public transportation terminals
e) Parks and recreational areas

Section 2. Violation of this ordinance shall be punishable by a fine of not less than Five Hundred Pesos (P500.00) but not more than Two Thousand Pesos (P2,000.00).

Section 3. This ordinance shall take effect fifteen (15) days after its publication.''',
                'year': 2024,
                'status': 'published',
                'category': 'Public Safety and Order'
            },
            {
                'number': 'ORD-2024-002',
                'title': 'An Ordinance Regulating the Operation of Food Establishments',
                'content': '''WHEREAS, food safety is essential for public health and welfare;
WHEREAS, proper regulation of food establishments ensures quality and safety standards;
WHEREAS, there is a need to establish guidelines for food establishment operations;

NOW, THEREFORE, BE IT ORDAINED by the Sangguniang Bayan that:

Section 1. All food establishments shall secure the necessary permits and licenses before operation.

Section 2. Food establishments must comply with sanitation and hygiene standards as prescribed by the Department of Health.

Section 3. Regular inspections shall be conducted to ensure compliance with this ordinance.

Section 4. Violation of this ordinance shall result in suspension or revocation of permits.

Section 5. This ordinance shall take effect immediately upon approval.''',
                'year': 2024,
                'status': 'approved',
                'category': 'Health and Sanitation'
            },
            {
                'number': 'ORD-2024-003',
                'title': 'An Ordinance Promoting Waste Segregation and Recycling',
                'content': '''WHEREAS, proper waste management is crucial for environmental protection;
WHEREAS, waste segregation at source reduces environmental pollution;
WHEREAS, recycling programs contribute to sustainable development;

NOW, THEREFORE, BE IT ORDAINED by the Sangguniang Bayan that:

Section 1. All households and establishments shall practice waste segregation into biodegradable, non-biodegradable, and recyclable materials.

Section 2. Collection schedules shall be established for different types of waste.

Section 3. Recycling centers shall be established in strategic locations.

Section 4. Educational campaigns on waste management shall be conducted regularly.

Section 5. Penalties for non-compliance shall range from P200.00 to P1,000.00.

Section 6. This ordinance shall take effect thirty (30) days after publication.''',
                'year': 2024,
                'status': 'published',
                'category': 'Environment and Natural Resources'
            },
            {
                'number': 'ORD-2023-015',
                'title': 'An Ordinance Establishing Speed Limits on Municipal Roads',
                'content': '''WHEREAS, road safety is a priority of the local government;
WHEREAS, excessive speed is a major cause of traffic accidents;
WHEREAS, speed limits help ensure the safety of motorists and pedestrians;

NOW, THEREFORE, BE IT ORDAINED by the Sangguniang Bayan that:

Section 1. Speed limits are hereby established as follows:
a) Residential areas: 30 km/h
b) School zones: 20 km/h
c) Commercial areas: 40 km/h
d) Main roads: 60 km/h

Section 2. Appropriate signages shall be installed to inform motorists of speed limits.

Section 3. Traffic enforcers are authorized to apprehend violators.

Section 4. Penalties for speeding violations shall be imposed according to the Traffic Code.

Section 5. This ordinance shall take effect immediately.''',
                'year': 2023,
                'status': 'published',
                'category': 'Traffic and Transportation'
            },
            {
                'number': 'ORD-2024-004',
                'title': 'An Ordinance Creating the Municipal Tourism Development Program',
                'content': '''WHEREAS, tourism development contributes to economic growth;
WHEREAS, the municipality has potential tourist attractions;
WHEREAS, a comprehensive tourism program is needed to promote local tourism;

NOW, THEREFORE, BE IT ORDAINED by the Sangguniang Bayan that:

Section 1. The Municipal Tourism Development Program is hereby created.

Section 2. The program shall focus on:
a) Promotion of local tourist destinations
b) Development of tourism infrastructure
c) Training of tourism service providers
d) Marketing and promotional activities

Section 3. A Tourism Development Council shall be established to oversee the program.

Section 4. Annual budget allocation shall be provided for program implementation.

Section 5. This ordinance shall take effect upon approval.''',
                'year': 2024,
                'status': 'draft',
                'category': 'General Administration'
            }
        ]
        
        for ord_data in ordinances_data:
            # Find category
            category = Category.objects.filter(name=ord_data['category']).first()
            
            ordinance, created = Ordinance.objects.get_or_create(
                ordinance_number=ord_data['number'],
                defaults={
                    'title': ord_data['title'],
                    'content': ord_data['content'],
                    'year_passed': ord_data['year'],
                    'status': ord_data['status'],
                    'category': category,
                    'created_by': admin_user,
                    'updated_by': admin_user,
                }
            )
            
            if created:
                # Add random sponsors
                random_sponsors = random.sample(sponsors, random.randint(1, 3))
                ordinance.sponsors.set(random_sponsors)
                
                # Create log entry
                OrdinanceLog.objects.create(
                    ordinance=ordinance,
                    user=admin_user,
                    action='Created ordinance',
                    notes='Sample data creation'
                )
                
                self.stdout.write(f'Created ordinance: {ord_data["number"]}')
            else:
                self.stdout.write(f'Ordinance already exists: {ord_data["number"]}')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully populated sample data!')
        )
        self.stdout.write(f'Categories: {Category.objects.count()}')
        self.stdout.write(f'Sponsors: {Sponsor.objects.count()}')
        self.stdout.write(f'Ordinances: {Ordinance.objects.count()}')
