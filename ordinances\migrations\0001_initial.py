# Generated by Django 4.2.17 on 2025-05-24 15:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(blank=True, max_length=120, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Ordinance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ordinance_number', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('title', models.CharField(max_length=255)),
                ('slug', models.SlugField(blank=True, max_length=300, unique=True)),
                ('content', models.TextField()),
                ('year_passed', models.PositiveIntegerField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('reviewed', 'Reviewed'), ('approved', 'Approved'), ('published', 'Published')], default='draft', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_archived', models.BooleanField(default=False)),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ordinances', to='ordinances.category')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_ordinances', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-year_passed', 'ordinance_number'],
            },
        ),
        migrations.CreateModel(
            name='Sponsor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('position', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='OrdinanceLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=255)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True)),
                ('ordinance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='ordinances.ordinance')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='ordinance',
            name='sponsors',
            field=models.ManyToManyField(blank=True, to='ordinances.sponsor'),
        ),
        migrations.AddField(
            model_name='ordinance',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_ordinances', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='Attachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='attachments/')),
                ('description', models.CharField(blank=True, max_length=255)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('ordinance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='ordinances.ordinance')),
            ],
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['status'], name='ordinances__status_19dabb_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['year_passed'], name='ordinances__year_pa_c412d3_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['category'], name='ordinances__categor_9cbb2b_idx'),
        ),
    ]
