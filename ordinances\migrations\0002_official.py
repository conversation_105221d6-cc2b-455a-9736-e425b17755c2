# Generated by Django 4.2.17 on 2025-05-24 16:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ordinances', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Official',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('position', models.Char<PERSON>ield(choices=[('mayor', 'Municipal Mayor'), ('vice_mayor', 'Vice Mayor'), ('councilor', 'Sangguniang Bayan Member'), ('secretary', 'Municipal Secretary'), ('treasurer', 'Municipal Treasurer')], max_length=50)),
                ('committee', models.CharField(blank=True, help_text='Committee or department headed', max_length=200)),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='officials/')),
                ('bio', models.TextField(blank=True, help_text='Brief biography or description')),
                ('achievements', models.TextField(blank=True, help_text='Key achievements (one per line)')),
                ('term_start', models.DateField(help_text='Start of current term')),
                ('term_end', models.DateField(help_text='End of current term')),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('retired', 'Retired')], default='active', max_length=20)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['order', 'position', 'name'],
            },
        ),
    ]
