# Generated by Django 4.2.17 on 2025-05-26 10:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ordinances', '0002_official'),
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Banner title or label', max_length=200)),
                ('message', models.TextField(help_text='Banner message content')),
                ('banner_type', models.CharField(choices=[('info', 'Information'), ('warning', 'Warning'), ('success', 'Success'), ('error', 'Error'), ('announcement', 'Announcement')], default='info', max_length=20)),
                ('color_scheme', models.CharField(choices=[('blue', 'Blue (Information)'), ('yellow', 'Yellow (Warning)'), ('green', 'Green (Success)'), ('red', 'Red (Error)'), ('purple', 'Purple (Announcement)'), ('gray', 'Gray (Neutral)')], default='blue', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Show this banner on the website')),
                ('is_dismissible', models.BooleanField(default=True, help_text='Allow users to dismiss this banner')),
                ('show_icon', models.BooleanField(default=True, help_text='Show icon in banner')),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now, help_text='When to start showing this banner')),
                ('end_date', models.DateTimeField(blank=True, help_text='When to stop showing this banner (optional)', null=True)),
                ('priority', models.PositiveIntegerField(default=1, help_text='Higher numbers show first (1-10)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_banners', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_banners', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Banner',
                'verbose_name_plural': 'Banners',
                'ordering': ['-priority', '-created_at'],
            },
        ),
    ]
