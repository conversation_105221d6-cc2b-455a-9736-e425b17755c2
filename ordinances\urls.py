from django.urls import path
from . import views

app_name = 'ordinances'

urlpatterns = [
    # Public URLs
    path('', views.home, name='home'),
    path('ordinances/', views.ordinance_list, name='ordinance_list'),
    path('ordinances/<slug:slug>/', views.ordinance_detail, name='ordinance_detail'),
    path('search-suggestions/', views.search_suggestions, name='search_suggestions'),
    path('ordinances/<slug:slug>/pdf/', views.export_ordinance_pdf, name='export_pdf'),

    # Secure Admin Access Portal
    path('admin-access/', views.admin_access_portal, name='admin_access_portal'),

    # Custom Authentication URLs
    path('admin-login/', views.admin_login, name='admin_login'),
    path('admin-logout/', views.admin_logout, name='admin_logout'),

    # Admin URLs (Protected)
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('admin-analytics/', views.admin_analytics, name='admin_analytics'),

    # Ordinance Management
    path('manage/ordinances/', views.admin_ordinance_list, name='admin_ordinance_list'),
    path('manage/ordinances/create/', views.admin_ordinance_create, name='admin_ordinance_create'),
    path('manage/ordinances/create-ajax/', views.admin_ordinance_create_ajax, name='admin_ordinance_create_ajax'),
    path('manage/ordinances/<int:ordinance_id>/edit/', views.admin_ordinance_edit, name='admin_ordinance_edit'),
    path('manage/ordinances/<int:ordinance_id>/delete/', views.admin_ordinance_delete, name='admin_ordinance_delete'),
    path('manage/ordinances/<int:ordinance_id>/status/', views.update_ordinance_status, name='update_ordinance_status'),

    # Category Management
    path('manage/categories/', views.admin_category_list, name='admin_category_list'),
    path('manage/categories/create/', views.admin_category_create, name='admin_category_create'),
    path('manage/categories/create-ajax/', views.admin_category_create_ajax, name='admin_category_create_ajax'),
    path('manage/categories/<int:category_id>/edit/', views.admin_category_edit, name='admin_category_edit'),
    path('manage/categories/<int:category_id>/delete/', views.admin_category_delete, name='admin_category_delete'),

    # Official management
    path('manage/officials/', views.admin_official_list, name='admin_official_list'),
    path('manage/officials/create/', views.admin_official_create, name='admin_official_create'),
    path('manage/officials/create-ajax/', views.admin_official_create_ajax, name='admin_official_create_ajax'),
    path('manage/officials/<int:official_id>/', views.admin_official_view, name='admin_official_view'),
    path('manage/officials/<int:official_id>/edit/', views.admin_official_edit, name='admin_official_edit'),
    path('manage/officials/<int:official_id>/delete/', views.admin_official_delete, name='admin_official_delete'),

    # Sponsor Management
    path('manage/sponsors/', views.admin_sponsor_list, name='admin_sponsor_list'),
    path('manage/sponsors/create/', views.admin_sponsor_create, name='admin_sponsor_create'),
    path('manage/sponsors/create-ajax/', views.admin_sponsor_create_ajax, name='admin_sponsor_create_ajax'),
    path('manage/sponsors/<int:sponsor_id>/edit/', views.admin_sponsor_edit, name='admin_sponsor_edit'),
    path('manage/sponsors/<int:sponsor_id>/delete/', views.admin_sponsor_delete, name='admin_sponsor_delete'),

    # Banner Management
    path('manage/banners/', views.admin_banner_list, name='admin_banner_list'),
    path('manage/banners/create/', views.admin_banner_create, name='admin_banner_create'),
    path('manage/banners/<int:banner_id>/edit/', views.admin_banner_edit, name='admin_banner_edit'),
    path('manage/banners/<int:banner_id>/delete/', views.admin_banner_delete, name='admin_banner_delete'),
    path('manage/banners/<int:banner_id>/toggle/', views.admin_banner_toggle, name='admin_banner_toggle'),
]
