/**
 * Three.js Navigation Animation System
 * Enhanced interactive navigation with particle effects and 3D animations
 * Optimized for government website professional appearance
 */

class NavigationThreeJS {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.particles = null;
        this.geometryWaves = null;
        this.canvas = document.getElementById('nav-canvas');
        this.isAnimating = false;
        this.animationId = null;
        this.mouseX = 0;
        this.mouseY = 0;
        this.targetRotationX = 0;
        this.targetRotationY = 0;

        if (this.canvas) {
            this.init();
            this.setupEventListeners();
        }
    }

    init() {
        // Scene setup
        this.scene = new THREE.Scene();

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / 64, 0.1, 1000);
        this.camera.position.z = 5;

        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            alpha: true,
            antialias: true,
            powerPreference: "high-performance"
        });
        this.renderer.setSize(window.innerWidth, 64);
        this.renderer.setClearColor(0x000000, 0);

        // Create effects
        this.createParticleSystem();
        this.createGeometryWaves();

        // Handle resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    createParticleSystem() {
        const particleCount = 150;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);

        // Government color palette
        const colors_palette = [
            new THREE.Color(0x123458), // Primary dark blue
            new THREE.Color(0xD4C9BE), // Primary beige
            new THREE.Color(0xF1EFEC), // Primary off-white
            new THREE.Color(0x2563eb), // Accent blue
            new THREE.Color(0x1e40af)  // Deep blue
        ];

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Position - spread across navigation area
            positions[i3] = (Math.random() - 0.5) * 25;
            positions[i3 + 1] = (Math.random() - 0.5) * 5;
            positions[i3 + 2] = (Math.random() - 0.5) * 8;

            // Color selection
            const color = colors_palette[Math.floor(Math.random() * colors_palette.length)];
            colors[i3] = color.r;
            colors[i3 + 1] = color.g;
            colors[i3 + 2] = color.b;

            // Size variation
            sizes[i] = Math.random() * 3 + 1;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const material = new THREE.PointsMaterial({
            size: 1.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.4,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true
        });

        this.particles = new THREE.Points(geometry, material);
        this.scene.add(this.particles);
    }

    createGeometryWaves() {
        const geometry = new THREE.PlaneGeometry(30, 6, 50, 10);
        const material = new THREE.MeshBasicMaterial({
            color: 0x123458,
            transparent: true,
            opacity: 0.05,
            wireframe: true,
            blending: THREE.AdditiveBlending
        });

        this.geometryWaves = new THREE.Mesh(geometry, material);
        this.geometryWaves.position.z = -2;
        this.scene.add(this.geometryWaves);
    }

    startAnimation(navItem) {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.canvas.style.opacity = '0.4';
        this.canvas.classList.add('active');

        // Customize animation based on nav item
        this.customizeForNavItem(navItem);
        this.animate();
    }

    stopAnimation() {
        this.isAnimating = false;
        this.canvas.style.opacity = '0.2';
        this.canvas.classList.remove('active');

        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    customizeForNavItem(navItem) {
        if (!this.particles) return;

        const material = this.particles.material;

        switch(navItem) {
            case 'home':
                material.color.setHex(0xF1EFEC); // Off-white
                break;
            case 'ordinances':
                material.color.setHex(0x123458); // Dark blue
                break;
            case 'admin':
                material.color.setHex(0x2563eb); // Accent blue
                break;
            case 'login':
                material.color.setHex(0xD4C9BE); // Beige
                break;
            default:
                material.color.setHex(0x123458);
        }
    }

    animate() {
        if (!this.isAnimating) return;

        this.animationId = requestAnimationFrame(() => this.animate());

        const time = Date.now() * 0.001;

        // Animate particles
        if (this.particles) {
            // Smooth rotation based on mouse position
            this.targetRotationX = (this.mouseY - 0.5) * 0.1;
            this.targetRotationY = (this.mouseX - 0.5) * 0.1;

            this.particles.rotation.x += (this.targetRotationX - this.particles.rotation.x) * 0.05;
            this.particles.rotation.y += (this.targetRotationY - this.particles.rotation.y) * 0.05;

            // Wave motion
            const positions = this.particles.geometry.attributes.position.array;
            for (let i = 0; i < positions.length; i += 3) {
                positions[i + 1] += Math.sin(time * 2 + positions[i] * 0.1) * 0.02;
                positions[i] += Math.cos(time * 1.5 + positions[i + 2] * 0.1) * 0.01;
            }
            this.particles.geometry.attributes.position.needsUpdate = true;
        }

        // Animate geometry waves
        if (this.geometryWaves) {
            this.geometryWaves.rotation.z = time * 0.1;

            const vertices = this.geometryWaves.geometry.attributes.position.array;
            for (let i = 0; i < vertices.length; i += 3) {
                vertices[i + 2] = Math.sin(time + vertices[i] * 0.1) * 0.5;
            }
            this.geometryWaves.geometry.attributes.position.needsUpdate = true;
        }

        this.renderer.render(this.scene, this.camera);
    }

    onWindowResize() {
        if (!this.camera || !this.renderer) return;

        this.camera.aspect = window.innerWidth / 64;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, 64);
    }

    setupEventListeners() {
        const navLinks = document.querySelectorAll('.nav-link-3d, .btn-primary-3d');
        const navigation = document.getElementById('main-navigation');

        // Mouse tracking for interactive effects
        if (navigation) {
            navigation.addEventListener('mousemove', (event) => {
                const rect = navigation.getBoundingClientRect();
                this.mouseX = (event.clientX - rect.left) / rect.width;
                this.mouseY = (event.clientY - rect.top) / rect.height;
            });
        }

        navLinks.forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                const navItem = e.target.getAttribute('data-nav-item');
                this.startAnimation(navItem);
                this.animateBackground(link, navItem);
            });

            link.addEventListener('mouseleave', () => {
                this.stopAnimation();
                this.resetBackground(link);
            });

            // Add click effect
            link.addEventListener('click', () => {
                this.addClickEffect(link);
            });
        });
    }

    animateBackground(element, navItem) {
        const bg = element.querySelector('.nav-bg, .btn-bg');
        if (!bg) return;

        // Different gradients for different nav items
        const gradients = {
            'home': 'linear-gradient(45deg, rgba(241,239,236,0.2), rgba(18,52,88,0.1))',
            'ordinances': 'linear-gradient(45deg, rgba(18,52,88,0.2), rgba(212,201,190,0.1))',
            'admin': 'linear-gradient(45deg, rgba(37,99,235,0.2), rgba(18,52,88,0.1))',
            'login': 'linear-gradient(45deg, rgba(212,201,190,0.2), rgba(18,52,88,0.1))',
            'default': 'linear-gradient(45deg, rgba(18,52,88,0.2), rgba(212,201,190,0.1))'
        };

        bg.style.opacity = '0.15';
        bg.style.background = gradients[navItem] || gradients.default;
        bg.style.transform = 'scale(1.05)';
    }

    resetBackground(element) {
        const bg = element.querySelector('.nav-bg, .btn-bg');
        if (bg) {
            bg.style.opacity = '0';
            bg.style.transform = 'scale(1)';
        }
    }

    addClickEffect(element) {
        element.style.transform = 'translateY(1px) scale(0.98)';
        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if Three.js is available
    if (typeof THREE !== 'undefined') {
        new NavigationThreeJS();
    } else {
        console.warn('Three.js not loaded - navigation animations disabled');
    }
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationThreeJS;
}
