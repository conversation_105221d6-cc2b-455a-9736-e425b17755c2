<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Access Portal - Municipality of Dumingag</title>

    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    {% load static %}
    <link rel="icon" type="image/png" href="{% static 'img/dumingag-logo.png' %}">

    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: white;
        }

        .admin-container {
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00d4ff;
            box-shadow: 0 8px 32px rgba(0, 212, 255, 0.3);
        }

        .section-card {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(10px);
        }

        .text-glow-white {
            text-shadow:
                2px 2px 4px rgba(0, 0, 0, 0.8),
                0 0 8px rgba(255, 255, 255, 0.3);
        }

        .text-glow-cyan {
            text-shadow:
                2px 2px 4px rgba(0, 0, 0, 0.8),
                0 0 8px rgba(0, 212, 255, 0.4);
        }

        .text-glow-amber {
            text-shadow:
                2px 2px 4px rgba(0, 0, 0, 0.8),
                0 0 8px rgba(251, 191, 36, 0.4);
        }

        .btn-primary {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: 2px solid #00d4ff;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(45deg, #0099cc, #00d4ff);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
            transform: translateY(-2px);
        }

        .status-indicator {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .floating-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 153, 204, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 100, 150, 0.15) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .security-badge {
            background: linear-gradient(45deg, #ff4444, #cc0000);
            border: 2px solid #ff6666;
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .grid-pattern {
            background-image:
                linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: grid-move 20s linear infinite;
        }

        @keyframes grid-move {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .logo-glow {
            filter: drop-shadow(0 0 10px #00d4ff);
        }
    </style>
</head>
<body class="relative overflow-hidden">
    <!-- Animated Background -->
    <div class="floating-bg"></div>
    <div class="grid-pattern fixed inset-0 opacity-30"></div>

    <!-- Main Container -->
    <div class="min-h-screen flex items-center justify-center p-4 relative z-10">
        <div class="admin-container rounded-3xl p-8 max-w-lg w-full">

            <!-- Header Section -->
            <div class="text-center mb-8">
                <!-- Security Badge -->
                <div class="security-badge inline-flex items-center px-6 py-3 rounded-full text-sm font-bold mb-6">
                    <i class="fas fa-shield-alt mr-3 text-lg"></i>
                    <span class="font-mono text-glow-white">RESTRICTED ACCESS</span>
                </div>

                <!-- Logo -->
                <div class="mb-6">
                    <img src="{% static 'img/dumingag-logo.png' %}"
                         alt="Dumingag Logo"
                         class="w-24 h-24 mx-auto logo-glow">
                </div>

                <!-- Title -->
                <h1 class="text-4xl font-bold text-white mb-3 font-mono tracking-wider text-glow-white">
                    ADMIN ACCESS PORTAL
                </h1>
                <p class="text-xl text-white font-mono tracking-wide text-glow-white">
                    MUNICIPALITY OF DUMINGAG
                </p>
                <div class="text-cyan-300 font-mono text-sm mt-3 text-glow-cyan">
                    SECURE ADMINISTRATIVE INTERFACE
                </div>
            </div>

            <!-- Warning Section -->
            <div class="section-card rounded-xl p-6 mb-8">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-amber-400 text-3xl status-indicator"></i>
                    </div>
                    <div class="text-left">
                        <h3 class="font-bold text-amber-300 mb-3 font-mono text-lg text-glow-amber">
                            AUTHORIZED PERSONNEL ONLY
                        </h3>
                        <p class="text-amber-100 text-sm font-mono leading-relaxed text-glow-white">
                            This area is restricted to authorized municipal staff only.
                            Unauthorized access is prohibited and monitored.
                            All activities are logged and tracked.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Access Button -->
            <div class="mb-8 text-center">
                <a href="{% url 'ordinances:admin_login' %}"
                   class="btn-primary inline-flex items-center justify-center px-10 py-5 text-white font-bold rounded-xl font-mono tracking-wide text-xl">
                    <i class="fas fa-key mr-4 text-2xl"></i>
                    <span class="text-glow-white">ADMIN LOGIN</span>
                    <i class="fas fa-arrow-right ml-4 text-2xl"></i>
                </a>
            </div>

            <!-- Security Features -->
            <div class="section-card rounded-xl p-6 mb-8">
                <div class="space-y-4">
                    <div class="flex items-center justify-center text-white text-sm font-mono">
                        <div class="w-4 h-4 bg-green-400 rounded-full status-indicator mr-4"></div>
                        <i class="fas fa-lock mr-3 text-green-400 text-lg"></i>
                        <span class="text-glow-white">SECURE SSL CONNECTION</span>
                    </div>
                    <div class="flex items-center justify-center text-white text-sm font-mono">
                        <div class="w-4 h-4 bg-blue-400 rounded-full status-indicator mr-4"></div>
                        <i class="fas fa-eye mr-3 text-blue-400 text-lg"></i>
                        <span class="text-glow-white">ACCESS LOGGING ENABLED</span>
                    </div>
                    <div class="flex items-center justify-center text-white text-sm font-mono">
                        <div class="w-4 h-4 bg-purple-400 rounded-full status-indicator mr-4"></div>
                        <i class="fas fa-shield-virus mr-3 text-purple-400 text-lg"></i>
                        <span class="text-glow-white">INTRUSION DETECTION ACTIVE</span>
                    </div>
                </div>
            </div>

            <!-- Back to Public Site -->
            <div class="text-center">
                <a href="{% url 'ordinances:home' %}"
                   class="section-card inline-flex items-center text-white hover:text-cyan-300 text-lg font-mono px-8 py-4 rounded-lg border-2 border-white/50 hover:border-cyan-400 transition-all duration-300">
                    <i class="fas fa-arrow-left mr-4 text-xl"></i>
                    <span class="text-glow-white">BACK TO PUBLIC SITE</span>
                </a>
            </div>
        </div>

        <!-- Floating Security Monitor -->
        <div class="fixed bottom-6 right-6 z-30">
            <div class="section-card rounded-lg p-4 text-white text-sm font-mono border-2 border-red-400">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-red-400 rounded-full status-indicator mr-3"></div>
                    <span class="text-glow-white">MONITORING ACTIVE</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Security monitoring simulation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔒 Admin access portal accessed at:', new Date().toISOString());

            // Security scan simulation
            setInterval(() => {
                console.log('🛡️ Security scan completed - All systems secure');
            }, 10000);
        });
    </script>
</body>
</html>
