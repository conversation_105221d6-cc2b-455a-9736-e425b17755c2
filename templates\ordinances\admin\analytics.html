{% extends 'base.html' %}

{% block title %}Advanced Analytics - Admin Dashboard{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Advanced Analytics</h1>
            <p class="text-lg text-gray-600">Deep insights into ordinance management and trends</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
            <a href="{% url 'ordinances:admin_dashboard' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                ← Back to Dashboard
            </a>
        </div>
    </div>
    
    <!-- Time Period Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900">{{ last_30_days_count }}</div>
                    <div class="text-sm text-gray-600">Last 30 Days</div>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900">{{ last_90_days_count }}</div>
                    <div class="text-sm text-gray-600">Last 90 Days</div>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm6-8a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900">{{ last_year_count }}</div>
                    <div class="text-sm text-gray-600">Last Year</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activity Trend Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Daily Activity Trend (Last 30 Days)</h3>
        <div class="relative" style="height: 300px;">
            <canvas id="activityChart"></canvas>
        </div>
    </div>
    
    <!-- Category Performance & User Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Category Performance -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Category Performance Analysis</h3>
            <div class="space-y-4">
                {% for category in category_performance %}
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">{{ category.name }}</h4>
                            <span class="text-sm text-gray-500">{{ category.total_ordinances }} total</span>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div>
                                <div class="text-lg font-bold text-green-600">{{ category.published_ordinances }}</div>
                                <div class="text-xs text-gray-500">Published</div>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-yellow-600">{{ category.draft_ordinances }}</div>
                                <div class="text-xs text-gray-500">Drafts</div>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-blue-600">
                                    {% if category.total_ordinances > 0 %}
                                        {% widthratio category.published_ordinances category.total_ordinances 100 %}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-500">Success Rate</div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" 
                                     style="width: {% if category.total_ordinances > 0 %}{% widthratio category.published_ordinances category.total_ordinances 100 %}%{% else %}0%{% endif %}"></div>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <p class="text-gray-500 text-center py-4">No category data available</p>
                {% endfor %}
            </div>
        </div>
        
        <!-- User Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Top Contributors</h3>
            <div class="space-y-4">
                {% for user in user_activity %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                <span class="text-indigo-600 font-semibold text-sm">
                                    {{ user.username|first|upper }}
                                </span>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                                <div class="text-xs text-gray-500">
                                    {{ user.created_count }} created, {{ user.updated_count }} updated
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-indigo-600">{{ user.total_activity }}</div>
                            <div class="text-xs text-gray-500">total actions</div>
                        </div>
                    </div>
                {% empty %}
                    <p class="text-gray-500 text-center py-4">No user activity data</p>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Status Transitions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Status Transition Analysis</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for transition in status_transitions %}
                <div class="bg-gray-50 rounded-lg p-4 text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ transition.count }}</div>
                    <div class="text-sm text-gray-600 mt-1">{{ transition.action }}</div>
                </div>
            {% empty %}
                <div class="col-span-full text-center py-8">
                    <p class="text-gray-500">No status transition data available</p>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

{% block extra_js %}
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activity Trend Chart
    const activityCtx = document.getElementById('activityChart').getContext('2d');
    const activityData = {{ activity_data|safe }};
    
    new Chart(activityCtx, {
        type: 'line',
        data: {
            labels: activityData.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'Created',
                data: activityData.map(item => item.created),
                borderColor: '#10B981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: 'Updated',
                data: activityData.map(item => item.updated),
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
});
</script>
{% endblock %}
{% endblock %}
