{% extends 'base.html' %}

{% block title %}
    {% if banner %}Edit Banner{% else %}Add New Banner{% endif %} - Admin
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-bullhorn text-blue-600 mr-3"></i>
                    {% if banner %}Edit Banner{% else %}Add New Banner{% endif %}
                </h1>
                <p class="text-gray-600 mt-2">
                    {% if banner %}
                        Update banner information for "{{ banner.title }}"
                    {% else %}
                        Create a new announcement banner for the website
                    {% endif %}
                </p>
            </div>
            <a href="{% url 'ordinances:admin_banner_list' %}"
               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Banners
            </a>
        </div>
    </div>

    <!-- Banner Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <form method="post" class="space-y-6 p-6">
            {% csrf_token %}
            
            <!-- Banner Content Section -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Banner Content</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Banner Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="title" name="title" required
                               value="{% if banner %}{{ banner.title }}{% endif %}"
                               placeholder="e.g., Important Notice"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label for="banner_type" class="block text-sm font-medium text-gray-700 mb-2">
                            Banner Type <span class="text-red-500">*</span>
                        </label>
                        <select id="banner_type" name="banner_type" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            {% for value, label in banner_types %}
                                <option value="{{ value }}" {% if banner and banner.banner_type == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="mt-6">
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                        Banner Message <span class="text-red-500">*</span>
                    </label>
                    <textarea id="message" name="message" rows="3" required
                              placeholder="Enter the banner message content..."
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">{% if banner %}{{ banner.message }}{% endif %}</textarea>
                </div>
            </div>

            <!-- Display Settings Section -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Display Settings</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="color_scheme" class="block text-sm font-medium text-gray-700 mb-2">
                            Color Scheme <span class="text-red-500">*</span>
                        </label>
                        <select id="color_scheme" name="color_scheme" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            {% for value, label in color_schemes %}
                                <option value="{{ value }}" {% if banner and banner.color_scheme == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                            Priority (1-10)
                        </label>
                        <input type="number" id="priority" name="priority" min="1" max="10"
                               value="{% if banner %}{{ banner.priority }}{% else %}1{% endif %}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-xs text-gray-500 mt-1">Higher numbers appear first</p>
                    </div>
                </div>
                
                <div class="mt-6 space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" 
                               {% if not banner or banner.is_active %}checked{% endif %}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-700">
                            Active (show this banner on the website)
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="is_dismissible" name="is_dismissible" 
                               {% if not banner or banner.is_dismissible %}checked{% endif %}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_dismissible" class="ml-2 block text-sm text-gray-700">
                            Dismissible (allow users to close this banner)
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="show_icon" name="show_icon" 
                               {% if not banner or banner.show_icon %}checked{% endif %}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="show_icon" class="ml-2 block text-sm text-gray-700">
                            Show icon in banner
                        </label>
                    </div>
                </div>
            </div>

            <!-- Scheduling Section -->
            <div class="pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Scheduling (Optional)</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Start Date & Time
                        </label>
                        <input type="datetime-local" id="start_date" name="start_date"
                               {% if banner and banner.start_date %}value="{{ banner.start_date|date:'Y-m-d\TH:i' }}"{% endif %}
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-xs text-gray-500 mt-1">When to start showing this banner</p>
                    </div>
                    
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
                            End Date & Time
                        </label>
                        <input type="datetime-local" id="end_date" name="end_date"
                               {% if banner and banner.end_date %}value="{{ banner.end_date|date:'Y-m-d\TH:i' }}"{% endif %}
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-xs text-gray-500 mt-1">When to stop showing this banner (leave empty for permanent)</p>
                    </div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                <div id="banner-preview" class="border border-gray-300 rounded-md p-4 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 flex-1">
                            <i id="preview-icon" class="fas fa-info-circle"></i>
                            <div>
                                <span id="preview-title" class="font-semibold text-sm">Banner Title:</span>
                                <span id="preview-message" class="ml-2 text-sm">Banner message will appear here...</span>
                            </div>
                        </div>
                        <button type="button" class="hover:opacity-75 transition-opacity ml-4 p-1">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'ordinances:admin_banner_list' %}" 
                   class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    {% if banner %}Update Banner{% else %}Create Banner{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Banner preview functionality
function updatePreview() {
    const title = document.getElementById('title').value || 'Banner Title';
    const message = document.getElementById('message').value || 'Banner message will appear here...';
    const bannerType = document.getElementById('banner_type').value;
    const colorScheme = document.getElementById('color_scheme').value;
    const showIcon = document.getElementById('show_icon').checked;
    
    // Update preview content
    document.getElementById('preview-title').textContent = title + ':';
    document.getElementById('preview-message').textContent = message;
    
    // Update icon based on banner type
    const iconMap = {
        'info': 'fas fa-info-circle',
        'warning': 'fas fa-exclamation-triangle',
        'success': 'fas fa-check-circle',
        'error': 'fas fa-times-circle',
        'announcement': 'fas fa-bullhorn'
    };
    
    const iconElement = document.getElementById('preview-icon');
    iconElement.className = iconMap[bannerType] || 'fas fa-info-circle';
    iconElement.style.display = showIcon ? 'inline' : 'none';
    
    // Update color scheme
    const colorMap = {
        'blue': 'bg-blue-100 text-blue-900 border-blue-200',
        'yellow': 'bg-yellow-400 text-yellow-900 border-yellow-500',
        'green': 'bg-green-100 text-green-900 border-green-200',
        'red': 'bg-red-100 text-red-900 border-red-200',
        'purple': 'bg-purple-100 text-purple-900 border-purple-200',
        'gray': 'bg-gray-100 text-gray-900 border-gray-200'
    };
    
    const previewElement = document.getElementById('banner-preview');
    previewElement.className = `border rounded-md p-4 ${colorMap[colorScheme] || colorMap['blue']}`;
}

// Add event listeners for real-time preview
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['title', 'message', 'banner_type', 'color_scheme', 'show_icon'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', updatePreview);
            element.addEventListener('change', updatePreview);
        }
    });
    
    // Initial preview update
    updatePreview();
});
</script>
{% endblock %}
