{% extends 'base.html' %}

{% block title %}Delete Category - Admin{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Delete Category</h1>
        <p class="text-lg text-gray-600">This action cannot be undone</p>
    </div>
    
    <!-- Category Details -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Category to be deleted:</h2>
        
        <div class="space-y-3">
            <div class="flex items-center justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Name:</span>
                <span class="text-sm text-gray-900">{{ category.name }}</span>
            </div>
            
            {% if category.description %}
                <div class="flex items-start justify-between py-2 border-b border-gray-200">
                    <span class="text-sm font-medium text-gray-600">Description:</span>
                    <span class="text-sm text-gray-900 text-right max-w-xs">{{ category.description }}</span>
                </div>
            {% endif %}
            
            <div class="flex items-center justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Slug:</span>
                <span class="text-sm text-gray-900">{{ category.slug }}</span>
            </div>
            
            <div class="flex items-center justify-between py-2">
                <span class="text-sm font-medium text-gray-600">Ordinances:</span>
                <span class="text-sm text-gray-900">{{ category.ordinances.count }} ordinance{{ category.ordinances.count|pluralize }}</span>
            </div>
        </div>
    </div>
    
    {% if category.ordinances.exists %}
        <!-- Cannot Delete Warning -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Cannot Delete Category</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>This category cannot be deleted because it contains {{ category.ordinances.count }} ordinance{{ category.ordinances.count|pluralize }}.</p>
                        <p class="mt-2">To delete this category, you must first:</p>
                        <ul class="list-disc list-inside mt-1 space-y-1">
                            <li>Move all ordinances to other categories, or</li>
                            <li>Delete all ordinances in this category</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Ordinances -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Ordinances in this Category</h3>
            <div class="space-y-3">
                {% for ordinance in category.ordinances.all %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ ordinance.ordinance_number }}</div>
                            <div class="text-sm text-gray-600">{{ ordinance.title|truncatechars:60 }}</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if ordinance.status == 'published' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Published
                                </span>
                            {% elif ordinance.status == 'approved' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Approved
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ ordinance.get_status_display }}
                                </span>
                            {% endif %}
                            <a href="{% url 'ordinances:admin_ordinance_edit' ordinance.id %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                Edit
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Actions for Category with Ordinances -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'ordinances:admin_category_list' %}" 
               class="bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-400 transition-colors text-center">
                Cancel
            </a>
            
            <a href="{% url 'ordinances:admin_category_edit' category.id %}" 
               class="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center">
                Edit Category
            </a>
            
            <a href="{% url 'ordinances:admin_ordinance_list' %}?category={{ category.slug }}" 
               class="bg-green-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors text-center">
                Manage Ordinances
            </a>
        </div>
        
    {% else %}
        <!-- Can Delete -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>This action will permanently delete the category. This action cannot be undone.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Actions for Empty Category -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'ordinances:admin_category_list' %}" 
               class="bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-400 transition-colors text-center">
                Cancel
            </a>
            
            <a href="{% url 'ordinances:admin_category_edit' category.id %}" 
               class="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center">
                Edit Instead
            </a>
            
            <form method="post" class="inline">
                {% csrf_token %}
                <button type="submit" 
                        class="bg-red-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-red-700 transition-colors w-full"
                        onclick="return confirm('Are you absolutely sure you want to delete this category? This action cannot be undone.')">
                    🗑️ Delete Permanently
                </button>
            </form>
        </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcut for cancel (Escape key)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            window.location.href = "{% url 'ordinances:admin_category_list' %}";
        }
    });
});
</script>
{% endblock %}
{% endblock %}
