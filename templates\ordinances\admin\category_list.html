{% extends 'base.html' %}

{% block title %}Manage Categories - Admin{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Manage Categories</h1>
            <p class="text-lg text-gray-600">Organize ordinances by categories</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
            <a href="{% url 'ordinances:admin_dashboard' %}" 
               class="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors">
                ← Back to Dashboard
            </a>
            <a href="{% url 'ordinances:admin_category_create' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Add New Category
            </a>
        </div>
    </div>
    
    <!-- Categories Grid -->
    {% if categories %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for category in categories %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ category.name }}</h3>
                            {% if category.description %}
                                <p class="text-sm text-gray-600 mb-3">{{ category.description|truncatewords:20 }}</p>
                            {% endif %}
                        </div>
                        <div class="ml-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ category.ordinance_count }} ordinance{{ category.ordinance_count|pluralize }}
                            </span>
                        </div>
                    </div>
                    
                    <!-- Category Stats -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                            <span>Usage</span>
                            <span>{{ category.ordinance_count }} total</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" 
                                 style="width: {% if categories %}{% widthratio category.ordinance_count categories.0.ordinance_count 100 %}%{% else %}0%{% endif %}"></div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <a href="{% url 'ordinances:admin_category_edit' category.id %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Edit
                            </a>
                            {% if category.ordinance_count == 0 %}
                                <a href="{% url 'ordinances:admin_category_delete' category.id %}" 
                                   class="text-red-600 hover:text-red-800 text-sm font-medium"
                                   onclick="return confirm('Are you sure you want to delete this category?')">
                                    Delete
                                </a>
                            {% else %}
                                <span class="text-gray-400 text-sm">Cannot delete (has ordinances)</span>
                            {% endif %}
                        </div>
                        
                        {% if category.ordinance_count > 0 %}
                            <a href="{% url 'ordinances:admin_ordinance_list' %}?category={{ category.slug }}" 
                               class="text-green-600 hover:text-green-800 text-sm font-medium">
                                View Ordinances →
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Summary Stats -->
        <div class="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Category Statistics</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ categories.count }}</div>
                    <div class="text-sm text-gray-600">Total Categories</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {% for category in categories %}{% if category.ordinance_count > 0 %}1{% endif %}{% endfor %}
                    </div>
                    <div class="text-sm text-gray-600">Categories in Use</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        {% for category in categories %}{{ category.ordinance_count|add:0 }}{% endfor %}
                    </div>
                    <div class="text-sm text-gray-600">Total Ordinances</div>
                </div>
            </div>
        </div>
        
    {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
                <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No categories yet</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first category to organize ordinances.</p>
            <a href="{% url 'ordinances:admin_category_create' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Create First Category
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
