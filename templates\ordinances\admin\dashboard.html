{% extends 'base.html' %}

{% block title %}Admin Dashboard - Sangguniang Bayan Ordinance System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">
                    Welcome back, {{ user.get_full_name|default:user.username }}! 👋
                </h1>
                <p class="text-blue-100 text-lg">
                    Municipality of Dumingag - Admin Dashboard
                </p>
                <p class="text-blue-200 text-sm mt-1">
                    Last login: {{ user.last_login|date:"M d, Y g:i A"|default:"First time login" }}
                </p>
            </div>
            <div class="hidden lg:block">
                <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
                    <i class="fas fa-user-shield text-3xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">System Overview</h2>
            <p class="text-gray-600">Comprehensive ordinance management and analytics</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
            <a href="{% url 'ordinances:admin_analytics' %}"
               class="bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 transition-colors">
                📊 Advanced Analytics
            </a>
            <a href="{% url 'admin:index' %}"
               class="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors">
                🛠️ Django Admin
            </a>
        </div>
    </div>

    <!-- Alert for Pending Actions -->
    {% if pending_review or pending_approval or pending_publication %}
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <strong>Action Required:</strong>
                        {% if pending_review %}{{ pending_review }} ordinance{{ pending_review|pluralize }} need{{ pending_review|pluralize:"s," }} review{% endif %}
                        {% if pending_approval %}{% if pending_review %}, {% endif %}{{ pending_approval }} need{{ pending_approval|pluralize:"s," }} approval{% endif %}
                        {% if pending_publication %}{% if pending_review or pending_approval %}, {% endif %}{{ pending_publication }} need{{ pending_publication|pluralize:"s," }} publication{% endif %}.
                    </p>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <button onclick="openAddOrdinanceModal()"
                class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg p-6 transition-colors w-full text-left">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Add Ordinance</div>
                    <div class="text-blue-100 text-sm">Create new ordinance</div>
                </div>
            </div>
        </button>

        <button onclick="openAddOfficialModal()"
                class="bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg p-6 transition-colors w-full text-left">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Add Official</div>
                    <div class="text-indigo-100 text-sm">Create new official</div>
                </div>
            </div>
        </button>

        <button onclick="openAddCategoryModal()"
                class="bg-green-600 hover:bg-green-700 text-white rounded-lg p-6 transition-colors w-full text-left">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Add Category</div>
                    <div class="text-green-100 text-sm">Create new category</div>
                </div>
            </div>
        </button>

        <button onclick="openAddSponsorModal()"
                class="bg-purple-600 hover:bg-purple-700 text-white rounded-lg p-6 transition-colors w-full text-left">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Add Sponsor</div>
                    <div class="text-purple-100 text-sm">Create new sponsor</div>
                </div>
            </div>
        </button>
    </div>

    <!-- Management Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <a href="{% url 'ordinances:admin_ordinance_list' %}"
           class="bg-gray-600 hover:bg-gray-700 text-white rounded-lg p-6 transition-colors">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Manage Ordinances</div>
                    <div class="text-gray-100 text-sm">View and edit all</div>
                </div>
            </div>
        </a>

        <a href="{% url 'ordinances:admin_official_list' %}"
           class="bg-teal-600 hover:bg-teal-700 text-white rounded-lg p-6 transition-colors">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Manage Officials</div>
                    <div class="text-teal-100 text-sm">View and edit all</div>
                </div>
            </div>
        </a>

        <a href="{% url 'ordinances:admin_category_list' %}"
           class="bg-orange-600 hover:bg-orange-700 text-white rounded-lg p-6 transition-colors">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Manage Categories</div>
                    <div class="text-orange-100 text-sm">View and edit all</div>
                </div>
            </div>
        </a>

        <a href="{% url 'ordinances:admin_sponsor_list' %}"
           class="bg-pink-600 hover:bg-pink-700 text-white rounded-lg p-6 transition-colors">
            <div class="flex items-center">
                <svg class="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                <div>
                    <div class="text-lg font-semibold">Manage Sponsors</div>
                    <div class="text-pink-100 text-sm">View and edit all</div>
                </div>
            </div>
        </a>
    </div>

    <!-- Additional Management Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <a href="{% url 'ordinances:admin_banner_list' %}"
           class="bg-purple-600 hover:bg-purple-700 text-white rounded-lg p-6 transition-colors">
            <div class="flex items-center">
                <i class="fas fa-bullhorn text-2xl mr-3"></i>
                <div>
                    <div class="text-lg font-semibold">Manage Banners</div>
                    <div class="text-purple-100 text-sm">Website announcements</div>
                </div>
            </div>
        </a>

        <button onclick="openAddOrdinanceModal()"
                class="bg-green-600 hover:bg-green-700 text-white rounded-lg p-6 transition-colors w-full text-left">
            <div class="flex items-center">
                <i class="fas fa-plus-circle text-2xl mr-3"></i>
                <div>
                    <div class="text-lg font-semibold">Quick Add</div>
                    <div class="text-green-100 text-sm">Create new ordinance</div>
                </div>
            </div>
        </button>

        <a href="{% url 'ordinances:admin_analytics' %}"
           class="bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg p-6 transition-colors">
            <div class="flex items-center">
                <i class="fas fa-chart-bar text-2xl mr-3"></i>
                <div>
                    <div class="text-lg font-semibold">Analytics</div>
                    <div class="text-indigo-100 text-sm">Detailed reports</div>
                </div>
            </div>
        </a>
    </div>

    <!-- Enhanced Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Ordinances -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900">{{ total_ordinances }}</div>
                    <div class="text-sm text-gray-600">Total Ordinances</div>
                    <div class="text-xs text-green-600 mt-1">
                        +{{ recent_ordinances_count }} this month
                    </div>
                </div>
            </div>
        </div>

        <!-- Draft Ordinances -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900">{{ draft_ordinances }}</div>
                    <div class="text-sm text-gray-600">Draft Ordinances</div>
                    <div class="text-xs text-gray-500 mt-1">
                        {{ draft_rate }}% of total
                    </div>
                </div>
            </div>
        </div>

        <!-- Published Ordinances -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900">{{ published_ordinances }}</div>
                    <div class="text-sm text-gray-600">Published Ordinances</div>
                    <div class="text-xs text-green-600 mt-1">
                        {{ completion_rate }}% completion rate
                    </div>
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <div class="text-2xl font-bold text-gray-900">{{ total_logs }}</div>
                    <div class="text-sm text-gray-600">Activity Logs</div>
                    <div class="text-xs text-blue-600 mt-1">
                        {{ recent_updates_count }} recent updates
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Status Distribution Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Status Distribution</h3>
            <div class="relative">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
            <div class="mt-4 grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-yellow-600">{{ reviewed_ordinances }}</div>
                    <div class="text-sm text-gray-600">Under Review</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">{{ approved_ordinances }}</div>
                    <div class="text-sm text-gray-600">Approved</div>
                </div>
            </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Monthly Creation Trend</h3>
            <div class="relative">
                <canvas id="monthlyChart" width="400" height="200"></canvas>
            </div>
            <div class="mt-4 text-center">
                <div class="text-sm text-gray-600">
                    Average: {{ total_ordinances|floatformat:0 }} ordinances per year
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Ordinances -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900">Recent Ordinances</h2>
                <a href="{% url 'ordinances:admin_ordinance_list' %}"
                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    View all →
                </a>
            </div>
        </div>

        {% if recent_ordinances %}
            <div class="overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ordinance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Category
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Year
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for ordinance in recent_ordinances %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ ordinance.ordinance_number }}
                                        </div>
                                        <div class="text-sm text-gray-500 max-w-xs truncate">
                                            {{ ordinance.title }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {% if ordinance.category %}
                                        {{ ordinance.category.name }}
                                    {% else %}
                                        <span class="text-gray-400">No category</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if ordinance.status == 'published' %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Published
                                        </span>
                                    {% elif ordinance.status == 'approved' %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Approved
                                        </span>
                                    {% elif ordinance.status == 'reviewed' %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                            Reviewed
                                        </span>
                                    {% else %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                            Draft
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ ordinance.year_passed }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ ordinance.created_at|date:"M d, Y" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <a href="{% url 'ordinances:admin_ordinance_edit' ordinance.id %}"
                                           class="text-blue-600 hover:text-blue-900">
                                            Edit
                                        </a>
                                        {% if ordinance.is_public %}
                                            <a href="{{ ordinance.get_absolute_url }}"
                                               class="text-green-600 hover:text-green-900">
                                                View
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="text-gray-400 mb-4">
                    <svg class="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No ordinances yet</h3>
                <p class="text-gray-600 mb-4">Get started by creating your first ordinance.</p>
                <a href="{% url 'ordinances:admin_ordinance_create' %}"
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    Create Ordinance
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Category Performance & Top Sponsors -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Category Performance -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Category Performance</h3>
            <div class="space-y-4">
                {% for category in category_stats|slice:":5" %}
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-gray-900">{{ category.name }}</span>
                                <span class="text-sm text-gray-500">{{ category.ordinance_count }}</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full"
                                     style="width: {% if total_ordinances > 0 %}{% widthratio category.ordinance_count total_ordinances 100 %}%{% else %}0%{% endif %}"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>{{ category.published_count }} published</span>
                                <span>{{ category.draft_count }} drafts</span>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <p class="text-gray-500 text-center py-4">No categories available</p>
                {% endfor %}
            </div>
        </div>

        <!-- Top Sponsors -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Top Sponsors</h3>
            <div class="space-y-4">
                {% for sponsor in top_sponsors %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-semibold text-sm">
                                    {{ sponsor.name|slice:":2"|upper }}
                                </span>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">{{ sponsor.name }}</div>
                                <div class="text-xs text-gray-500">{{ sponsor.position }}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-blue-600">{{ sponsor.ordinance_count }}</div>
                            <div class="text-xs text-gray-500">ordinances</div>
                        </div>
                    </div>
                {% empty %}
                    <p class="text-gray-500 text-center py-4">No sponsors available</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Recent Activity Logs -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div class="divide-y divide-gray-200">
            {% for log in recent_logs %}
                <div class="px-6 py-4 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ log.action }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ log.ordinance.ordinance_number }} - {{ log.ordinance.title|truncatechars:50 }}
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-500">{{ log.user.username }}</div>
                            <div class="text-xs text-gray-400">{{ log.timestamp|timesince }} ago</div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="px-6 py-8 text-center">
                    <p class="text-gray-500">No recent activity</p>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- System Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-indigo-600">{{ total_categories }}</div>
            <div class="text-sm text-gray-600">Categories</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-pink-600">{{ total_sponsors }}</div>
            <div class="text-sm text-gray-600">Sponsors</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-teal-600">{{ total_attachments }}</div>
            <div class="text-sm text-gray-600">Attachments</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-red-600">{{ archived_ordinances }}</div>
            <div class="text-sm text-gray-600">Archived</div>
        </div>
    </div>

    <!-- Officials Overview -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Municipal Officials</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Mayor -->
            <div class="text-center">
                {% if officials.mayor %}
                    <div class="w-16 h-16 mx-auto mb-3 rounded-full overflow-hidden bg-blue-100">
                        {% if officials.mayor.profile_picture %}
                            <img src="{{ officials.mayor.profile_picture.url }}"
                                 alt="{{ officials.mayor.name }}"
                                 class="w-full h-full object-cover">
                        {% else %}
                            <div class="w-full h-full flex items-center justify-center">
                                <i class="fas fa-user-tie text-2xl text-blue-600"></i>
                            </div>
                        {% endif %}
                    </div>
                    <h4 class="font-semibold text-gray-900">{{ officials.mayor.name }}</h4>
                    <p class="text-sm text-blue-600">{{ officials.mayor.get_position_display }}</p>
                {% else %}
                    <div class="w-16 h-16 mx-auto mb-3 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-user-plus text-gray-400"></i>
                    </div>
                    <h4 class="font-semibold text-gray-500">Mayor</h4>
                    <p class="text-sm text-gray-400">Not assigned</p>
                    <button onclick="openAddOfficialModal()"
                            class="text-xs text-blue-600 hover:text-blue-800 mt-1 inline-block">
                        Add Mayor
                    </button>
                {% endif %}
            </div>

            <!-- Vice Mayor -->
            <div class="text-center">
                {% if officials.vice_mayor %}
                    <div class="w-16 h-16 mx-auto mb-3 rounded-full overflow-hidden bg-green-100">
                        {% if officials.vice_mayor.profile_picture %}
                            <img src="{{ officials.vice_mayor.profile_picture.url }}"
                                 alt="{{ officials.vice_mayor.name }}"
                                 class="w-full h-full object-cover">
                        {% else %}
                            <div class="w-full h-full flex items-center justify-center">
                                <i class="fas fa-user-tie text-2xl text-green-600"></i>
                            </div>
                        {% endif %}
                    </div>
                    <h4 class="font-semibold text-gray-900">{{ officials.vice_mayor.name }}</h4>
                    <p class="text-sm text-green-600">{{ officials.vice_mayor.get_position_display }}</p>
                {% else %}
                    <div class="w-16 h-16 mx-auto mb-3 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-user-plus text-gray-400"></i>
                    </div>
                    <h4 class="font-semibold text-gray-500">Vice Mayor</h4>
                    <p class="text-sm text-gray-400">Not assigned</p>
                    <button onclick="openAddOfficialModal()"
                            class="text-xs text-blue-600 hover:text-blue-800 mt-1 inline-block">
                        Add Vice Mayor
                    </button>
                {% endif %}
            </div>

            <!-- Council Members Count -->
            <div class="text-center">
                <div class="w-16 h-16 mx-auto mb-3 rounded-full bg-purple-100 flex items-center justify-center">
                    {% if officials.council_members.count > 0 %}
                        <span class="text-2xl font-bold text-purple-600">{{ officials.council_members.count }}</span>
                    {% else %}
                        <i class="fas fa-users text-2xl text-purple-600"></i>
                    {% endif %}
                </div>
                <h4 class="font-semibold text-gray-900">Council Members</h4>
                {% if officials.council_members.count > 0 %}
                    <p class="text-sm text-purple-600">{{ officials.council_members.count }} Active Member{{ officials.council_members.count|pluralize }}</p>
                {% else %}
                    <p class="text-sm text-gray-400">No members assigned</p>
                {% endif %}
                <button onclick="openAddOfficialModal()"
                        class="text-xs text-blue-600 hover:text-blue-800 mt-1 inline-block">
                    {% if officials.council_members.count > 0 %}Add More{% else %}Add Members{% endif %}
                </button>
            </div>
        </div>

        <!-- Quick Actions for Officials -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="flex flex-wrap gap-2 justify-center">
                <a href="{% url 'ordinances:admin_official_list' %}"
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                    <i class="fas fa-users mr-1"></i>Manage Officials
                </a>
                <button onclick="openAddOfficialModal()"
                        class="bg-green-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-700 transition-colors">
                    <i class="fas fa-user-plus mr-1"></i>Add Official
                </button>
                <a href="{% url 'admin:ordinances_official_changelist' %}"
                   class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-purple-700 transition-colors">
                    <i class="fas fa-cog mr-1"></i>Django Admin
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
{% include 'ordinances/admin/modals/add_official_modal.html' %}

{% include 'ordinances/admin/modals/add_ordinance_modal.html' %}

{% include 'ordinances/admin/modals/add_category_modal.html' %}
{% include 'ordinances/admin/modals/add_sponsor_modal.html' %}

{% block extra_js %}
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Status Distribution Pie Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusData = {{ status_distribution|safe }};

    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: statusData.map(item => item.status),
            datasets: [{
                data: statusData.map(item => item.count),
                backgroundColor: statusData.map(item => item.color),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // Monthly Trend Line Chart
    const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
    const monthlyData = {{ monthly_data|safe }};

    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: monthlyData.map(item => item.month),
            datasets: [{
                label: 'Ordinances Created',
                data: monthlyData.map(item => item.count),
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // ===== MODAL FUNCTIONALITY =====
    // Load modular modal scripts

    // ===== ORDINANCE MODAL FUNCTIONS =====

    window.openAddOrdinanceModal = function() {
        const modal = document.getElementById('addOrdinanceModal');
        const modalContent = document.getElementById('ordinanceModalContent');

        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
    };

    window.closeAddOrdinanceModal = function() {
        const modal = document.getElementById('addOrdinanceModal');
        const modalContent = document.getElementById('ordinanceModalContent');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.getElementById('addOrdinanceForm').reset();
        }, 300);
    };

    // Close ordinance modal when clicking outside
    document.getElementById('addOrdinanceModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeAddOrdinanceModal();
        }
    });

    // Ordinance form submission
    document.getElementById('addOrdinanceForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('ordinanceSubmitBtn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
        `;

        // Validate form
        const ordinanceNumber = document.getElementById('ordinance_number').value.trim();
        const title = document.getElementById('title').value.trim();
        const content = document.getElementById('content').value.trim();
        const yearPassed = document.getElementById('year_passed').value;

        if (!ordinanceNumber || !title || !content || !yearPassed) {
            showNotification('Please fill in all required fields.', 'error');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            return;
        }

        // Submit form via AJAX
        const formData = new FormData(this);

        fetch('{% url "ordinances:admin_ordinance_create_ajax" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Ordinance created successfully!', 'success');
                closeAddOrdinanceModal();
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error creating ordinance.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });

    // ===== CATEGORY MODAL FUNCTIONS =====

    window.openAddCategoryModal = function() {
        const modal = document.getElementById('addCategoryModal');
        const modalContent = document.getElementById('categoryModalContent');

        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
    };

    window.closeAddCategoryModal = function() {
        const modal = document.getElementById('addCategoryModal');
        const modalContent = document.getElementById('categoryModalContent');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.getElementById('addCategoryForm').reset();
        }, 300);
    };

    // Close category modal when clicking outside
    document.getElementById('addCategoryModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeAddCategoryModal();
        }
    });

    // Category form submission
    document.getElementById('addCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('categorySubmitBtn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
        `;

        // Validate form
        const name = document.getElementById('category_name').value.trim();

        if (!name) {
            showNotification('Category name is required.', 'error');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            return;
        }

        // Submit form via AJAX
        const formData = new FormData(this);

        fetch('{% url "ordinances:admin_category_create_ajax" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Category created successfully!', 'success');
                closeAddCategoryModal();
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error creating category.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });

    // ===== SPONSOR MODAL FUNCTIONS =====

    window.openAddSponsorModal = function() {
        const modal = document.getElementById('addSponsorModal');
        const modalContent = document.getElementById('sponsorModalContent');

        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
    };

    window.closeAddSponsorModal = function() {
        const modal = document.getElementById('addSponsorModal');
        const modalContent = document.getElementById('sponsorModalContent');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.getElementById('addSponsorForm').reset();
        }, 300);
    };

    // Close sponsor modal when clicking outside
    document.getElementById('addSponsorModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeAddSponsorModal();
        }
    });

    // Sponsor form submission
    document.getElementById('addSponsorForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('sponsorSubmitBtn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
        `;

        // Validate form
        const name = document.getElementById('sponsor_name').value.trim();
        const position = document.getElementById('sponsor_position').value.trim();

        if (!name || !position) {
            showNotification('Name and position are required.', 'error');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            return;
        }

        // Submit form via AJAX
        const formData = new FormData(this);

        fetch('{% url "ordinances:admin_sponsor_create_ajax" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Sponsor created successfully!', 'success');
                closeAddSponsorModal();
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error creating sponsor.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
});
</script>

<!-- Modal Scripts -->
<script>{% include 'ordinances/admin/modals/scripts/modal_utils.js' %}</script>
<script>{% include 'ordinances/admin/modals/scripts/official_modal.js' %}</script>
<script>{% include 'ordinances/admin/modals/scripts/ordinance_modal.js' %}</script>
<script>{% include 'ordinances/admin/modals/scripts/category_modal.js' %}</script>
<script>{% include 'ordinances/admin/modals/scripts/sponsor_modal.js' %}</script>

{% endblock %}
{% endblock %}
