/**
 * Category Modal JavaScript
 * Handles the Add Category modal functionality
 */

// Category Modal Functions
window.openAddCategoryModal = function() {
    openModal('addCategoryModal', 'categoryModalContent');
};

window.closeAddCategoryModal = function() {
    closeModal('addCategoryModal', 'categoryModalContent', 'addCategoryForm');
};

// Initialize category modal functionality
function initializeCategoryModal() {
    // Setup click outside to close
    setupClickOutsideClose('addCategoryModal', closeAddCategoryModal);
    
    // Setup form submission
    handleFormSubmission(
        'addCategoryForm',
        '{% url "ordinances:admin_category_create_ajax" %}',
        'Category created successfully!',
        closeAddCategoryModal
    );
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeCategoryModal();
});
