{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Official - {{ official.name }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">
            <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
            Delete Official
        </h1>
        <p class="text-gray-600 mt-2">
            This action cannot be undone. Please confirm deletion.
        </p>
    </div>

    <!-- Warning Card -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
        <div class="flex items-start">
            <i class="fas fa-exclamation-triangle text-red-600 text-2xl mr-4 mt-1"></i>
            <div>
                <h3 class="text-lg font-semibold text-red-800 mb-2">
                    Warning: Permanent Deletion
                </h3>
                <p class="text-red-700 mb-4">
                    You are about to permanently delete the following official:
                </p>
                
                <!-- Official Info -->
                <div class="bg-white rounded-lg p-4 border border-red-200">
                    <div class="flex items-center space-x-4">
                        {% if official.profile_picture %}
                            <img src="{{ official.profile_picture.url }}" 
                                 alt="{{ official.name }}" 
                                 class="w-16 h-16 rounded-full object-cover border-2 border-gray-300">
                        {% else %}
                            <div class="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center border-2 border-gray-300">
                                <i class="fas fa-user text-2xl text-gray-400"></i>
                            </div>
                        {% endif %}
                        
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">{{ official.name }}</h4>
                            <p class="text-gray-600">{{ official.get_position_display }}</p>
                            {% if official.committee %}
                                <p class="text-sm text-gray-500">{{ official.committee }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 text-red-700">
                    <p class="font-semibold">This will permanently remove:</p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>Official's profile and information</li>
                        <li>Profile picture (if uploaded)</li>
                        <li>Biography and achievements</li>
                        <li>Term information</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmation Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">
            Confirm Deletion
        </h3>
        
        <p class="text-gray-600 mb-6">
            Type <strong>"{{ official.name }}"</strong> to confirm deletion:
        </p>
        
        <form method="post" id="deleteForm">
            {% csrf_token %}
            
            <div class="mb-6">
                <input type="text" 
                       id="confirmName" 
                       placeholder="Enter official's name to confirm"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                       required>
            </div>
            
            <div class="flex justify-between items-center">
                <a href="{% url 'ordinances:admin_official_view' official.id %}" 
                   class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Cancel
                </a>
                
                <button type="submit" 
                        id="deleteButton"
                        class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                    <i class="fas fa-trash mr-2"></i>Delete Official
                </button>
            </div>
        </form>
    </div>

    <!-- Additional Information -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-600 mr-3 mt-1"></i>
            <div class="text-blue-800">
                <h4 class="font-semibold mb-1">Alternative Actions</h4>
                <p class="text-sm">
                    Instead of deleting, you can:
                </p>
                <ul class="text-sm mt-2 space-y-1">
                    <li>• Set status to "Inactive" to hide from public view</li>
                    <li>• Set status to "Retired" for historical records</li>
                    <li>• Edit the official's information if there are errors</li>
                </ul>
                <div class="mt-3 space-x-2">
                    <a href="{% url 'ordinances:admin_official_edit' official.id %}" 
                       class="text-blue-600 hover:underline text-sm">
                        Edit Official Instead
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmInput = document.getElementById('confirmName');
    const deleteButton = document.getElementById('deleteButton');
    const deleteForm = document.getElementById('deleteForm');
    const expectedName = "{{ official.name }}";
    
    // Enable/disable delete button based on input
    confirmInput.addEventListener('input', function() {
        const inputValue = this.value.trim();
        if (inputValue === expectedName) {
            deleteButton.disabled = false;
            deleteButton.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            deleteButton.disabled = true;
            deleteButton.classList.add('opacity-50', 'cursor-not-allowed');
        }
    });
    
    // Prevent form submission if name doesn't match
    deleteForm.addEventListener('submit', function(e) {
        const inputValue = confirmInput.value.trim();
        if (inputValue !== expectedName) {
            e.preventDefault();
            alert('Please enter the exact name to confirm deletion.');
            confirmInput.focus();
            return false;
        }
        
        // Final confirmation
        if (!confirm('Are you absolutely sure you want to delete this official? This action cannot be undone.')) {
            e.preventDefault();
            return false;
        }
    });
    
    // Focus on input when page loads
    confirmInput.focus();
});
</script>
{% endblock %}
