{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if official %}Edit Official{% else %}Add New Official{% endif %} - Admin Dashboard
{% endblock %}

{% block extra_css %}
<style>
    .preview-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid #e5e7eb;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .file-upload-area {
        border: 3px dashed #cbd5e1;
        border-radius: 12px;
        padding: 2.5rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        position: relative;
        overflow: hidden;
    }

    .file-upload-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
    }

    .file-upload-area:hover::before {
        transform: translateX(100%);
    }

    .file-upload-area:hover {
        border-color: #3b82f6;
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    }

    .file-upload-area.dragover {
        border-color: #10b981;
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
        transform: scale(1.02);
        box-shadow: 0 12px 30px rgba(16, 185, 129, 0.2);
    }

    .upload-icon {
        font-size: 3rem;
        color: #64748b;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .file-upload-area:hover .upload-icon {
        color: #3b82f6;
        transform: scale(1.1);
    }

    .file-upload-area.dragover .upload-icon {
        color: #10b981;
        transform: scale(1.2) rotate(5deg);
    }

    .upload-text {
        font-size: 1.1rem;
        font-weight: 600;
        color: #475569;
        margin-bottom: 0.5rem;
        transition: color 0.3s ease;
    }

    .file-upload-area:hover .upload-text {
        color: #3b82f6;
    }

    .upload-subtext {
        font-size: 0.875rem;
        color: #64748b;
        transition: color 0.3s ease;
    }

    .file-upload-area:hover .upload-subtext {
        color: #1e40af;
    }

    .browse-button {
        display: inline-block;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        margin-top: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .file-upload-area:hover .browse-button {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }

    /* Pulsing animation for better visibility */
    @keyframes pulse-upload {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.8;
        }
    }

    .file-upload-area:not(:hover) .upload-icon {
        animation: pulse-upload 2s ease-in-out infinite;
    }

    /* Loading state */
    .file-upload-area.uploading {
        pointer-events: none;
        opacity: 0.7;
    }

    .file-upload-area.uploading .upload-icon {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Success state */
    .file-upload-area.success {
        border-color: #10b981;
        background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    }

    .file-upload-area.success .upload-icon {
        color: #10b981;
    }

    /* Enhanced visual cues */
    .upload-hint {
        position: absolute;
        top: -10px;
        right: -10px;
        background: #3b82f6;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        animation: bounce 2s ease-in-out infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    .form-section {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .achievement-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .achievement-item input {
        flex: 1;
        margin-right: 0.5rem;
    }

    .btn-remove {
        background-color: #ef4444;
        color: white;
        border: none;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
    }

    .btn-add {
        background-color: #10b981;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-user-tie text-blue-600 mr-3"></i>
                    {% if official %}Edit Official{% else %}Add New Official{% endif %}
                </h1>
                <p class="text-gray-600 mt-2">
                    {% if official %}
                        Update information for {{ official.name }}
                    {% else %}
                        Add a new municipal official to the system
                    {% endif %}
                </p>
            </div>
            <a href="{% url 'ordinances:admin_official_list' %}"
               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Officials
            </a>
        </div>
    </div>

    <!-- Form -->
    <form method="post" enctype="multipart/form-data" id="officialForm">
        {% csrf_token %}

        <!-- Basic Information Section -->
        <div class="form-section">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-user text-blue-600 mr-2"></i>Basic Information
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Profile Picture -->
                <div class="lg:col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Profile Picture</label>

                    <!-- Current Image Preview -->
                    <div class="mb-4 text-center">
                        {% if official.profile_picture %}
                            <img src="{{ official.profile_picture.url }}"
                                 alt="{{ official.name }}"
                                 class="preview-image mx-auto"
                                 id="imagePreview">
                        {% else %}
                            <div class="w-48 h-48 mx-auto bg-gray-100 rounded-full flex items-center justify-center" id="imagePreview">
                                <i class="fas fa-user text-6xl text-gray-400"></i>
                            </div>
                        {% endif %}
                    </div>

                    <!-- File Upload Area -->
                    <div class="file-upload-area" id="uploadArea">
                        <input type="file"
                               name="profile_picture"
                               id="profilePicture"
                               accept="image/*"
                               class="hidden">

                        <!-- Visual Hint Indicator -->
                        <div class="upload-hint" id="uploadHint">
                            <i class="fas fa-plus"></i>
                        </div>

                        <!-- Upload Icon with Animation -->
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>

                        <!-- Main Upload Text -->
                        <div class="upload-text">
                            Click to upload or drag & drop
                        </div>

                        <!-- Subtext with file info -->
                        <div class="upload-subtext">
                            PNG, JPG, JPEG up to 5MB
                        </div>

                        <!-- Browse Button -->
                        <div class="browse-button">
                            <i class="fas fa-folder-open mr-2"></i>
                            Browse Files
                        </div>

                        <!-- Additional Visual Cue -->
                        <div class="upload-subtext mt-2">
                            <i class="fas fa-mouse-pointer mr-1"></i>
                            Click anywhere in this area
                        </div>
                    </div>
                </div>

                <!-- Basic Details -->
                <div class="lg:col-span-2 space-y-4">
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                            Full Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               name="name"
                               id="name"
                               value="{{ official.name|default:'' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Hon. [Full Name]"
                               required>
                    </div>

                    <!-- Position -->
                    <div>
                        <label for="position" class="block text-sm font-medium text-gray-700 mb-1">
                            Position <span class="text-red-500">*</span>
                        </label>
                        <select name="position"
                                id="position"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required>
                            <option value="">Select Position</option>
                            {% for value, label in position_choices %}
                                <option value="{{ value }}" {% if official.position == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Committee -->
                    <div>
                        <label for="committee" class="block text-sm font-medium text-gray-700 mb-1">
                            Committee/Department
                        </label>
                        <input type="text"
                               name="committee"
                               id="committee"
                               value="{{ official.committee|default:'' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="e.g., Committee on Health, Office of the Mayor">
                    </div>

                    <!-- Display Order -->
                    <div>
                        <label for="order" class="block text-sm font-medium text-gray-700 mb-1">
                            Display Order
                        </label>
                        <input type="number"
                               name="order"
                               id="order"
                               value="{{ official.order|default:0 }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               min="0"
                               placeholder="0">
                        <p class="text-sm text-gray-500 mt-1">Lower numbers appear first (Mayor=1, Vice Mayor=2, etc.)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Term Information Section -->
        <div class="form-section">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-calendar text-green-600 mr-2"></i>Term Information
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Term Start -->
                <div>
                    <label for="term_start" class="block text-sm font-medium text-gray-700 mb-1">
                        Term Start Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date"
                           name="term_start"
                           id="term_start"
                           value="{{ official.term_start|date:'Y-m-d'|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           required>
                </div>

                <!-- Term End -->
                <div>
                    <label for="term_end" class="block text-sm font-medium text-gray-700 mb-1">
                        Term End Date <span class="text-red-500">*</span>
                    </label>
                    <input type="date"
                           name="term_end"
                           id="term_end"
                           value="{{ official.term_end|date:'Y-m-d'|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           required>
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                        Status
                    </label>
                    <select name="status"
                            id="status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if official.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>

        <!-- Biography Section -->
        <div class="form-section">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">
                <i class="fas fa-file-alt text-purple-600 mr-2"></i>Biography & Achievements
            </h2>

            <!-- Bio -->
            <div class="mb-6">
                <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">
                    Biography
                </label>
                <textarea name="bio"
                          id="bio"
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Brief biography or description of the official's background and experience...">{{ official.bio|default:'' }}</textarea>
            </div>

            <!-- Achievements -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Key Achievements
                </label>
                <div id="achievementsContainer">
                    {% if official.get_achievements_list %}
                        {% for achievement in official.get_achievements_list %}
                            <div class="achievement-item">
                                <input type="text"
                                       name="achievements[]"
                                       value="{{ achievement }}"
                                       class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="Enter achievement">
                                <button type="button" class="btn-remove" onclick="removeAchievement(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="achievement-item">
                            <input type="text"
                                   name="achievements[]"
                                   class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="Enter achievement">
                            <button type="button" class="btn-remove" onclick="removeAchievement(this)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    {% endif %}
                </div>
                <button type="button" class="btn-add" onclick="addAchievement()">
                    <i class="fas fa-plus mr-1"></i>Add Achievement
                </button>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-between items-center bg-gray-50 px-6 py-4 rounded-lg">
            <a href="{% url 'ordinances:admin_official_list' %}"
               class="text-gray-600 hover:text-gray-800 transition-colors">
                <i class="fas fa-arrow-left mr-1"></i>Cancel
            </a>

            <div class="space-x-3">
                {% if official %}
                    <button type="button"
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                            onclick="confirmDelete()">
                        <i class="fas fa-trash mr-1"></i>Delete
                    </button>
                {% endif %}

                <button type="submit"
                        class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-1"></i>
                    {% if official %}Update Official{% else %}Create Official{% endif %}
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Delete Confirmation Modal -->
{% if official %}
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
            <p class="text-gray-600 mb-6">
                Are you sure you want to delete {{ official.name }}? This action cannot be undone.
            </p>
            <div class="flex justify-end space-x-3">
                <button type="button"
                        class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                        onclick="closeDeleteModal()">
                    Cancel
                </button>
                <form method="post" action="{% url 'ordinances:admin_official_delete' official.id %}" class="inline">
                    {% csrf_token %}
                    <button type="submit"
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload handling
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('profilePicture');
    const imagePreview = document.getElementById('imagePreview');

    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // File input change
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            previewImage(file);
        }
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type.startsWith('image/')) {
                fileInput.files = files;
                previewImage(file);
            }
        }
    });

    function previewImage(file) {
        const uploadArea = document.getElementById('uploadArea');
        const uploadHint = document.getElementById('uploadHint');

        // Show loading state
        uploadArea.classList.add('uploading');

        const reader = new FileReader();
        reader.onload = function(e) {
            // Update preview
            imagePreview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="preview-image mx-auto">`;

            // Show success state
            uploadArea.classList.remove('uploading');
            uploadArea.classList.add('success');

            // Hide the hint indicator
            if (uploadHint) {
                uploadHint.style.display = 'none';
            }

            // Reset success state after 2 seconds
            setTimeout(() => {
                uploadArea.classList.remove('success');
            }, 2000);
        };

        reader.onerror = function() {
            uploadArea.classList.remove('uploading');
            alert('Error reading file. Please try again.');
        };

        reader.readAsDataURL(file);
    }

    // Form validation
    const form = document.getElementById('officialForm');
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const position = document.getElementById('position').value;
        const termStart = document.getElementById('term_start').value;
        const termEnd = document.getElementById('term_end').value;

        if (!name || !position || !termStart || !termEnd) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return;
        }

        // Validate term dates
        if (new Date(termStart) >= new Date(termEnd)) {
            e.preventDefault();
            alert('Term end date must be after term start date.');
            return;
        }
    });

    // Position-based suggestions
    const positionSelect = document.getElementById('position');
    const committeeInput = document.getElementById('committee');

    positionSelect.addEventListener('change', function() {
        const position = this.value;
        let suggestion = '';

        switch(position) {
            case 'mayor':
                suggestion = 'Office of the Municipal Mayor';
                break;
            case 'vice_mayor':
                suggestion = 'Office of the Vice Mayor';
                break;
            case 'councilor':
                suggestion = 'Committee on ';
                break;
            case 'secretary':
                suggestion = 'Municipal Secretary Office';
                break;
            case 'treasurer':
                suggestion = 'Municipal Treasurer Office';
                break;
        }

        if (suggestion && !committeeInput.value) {
            committeeInput.value = suggestion;
        }
    });
});

// Achievement management functions
function addAchievement() {
    const container = document.getElementById('achievementsContainer');
    const newItem = document.createElement('div');
    newItem.className = 'achievement-item';
    newItem.innerHTML = `
        <input type="text"
               name="achievements[]"
               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
               placeholder="Enter achievement">
        <button type="button" class="btn-remove" onclick="removeAchievement(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(newItem);

    // Focus on the new input
    newItem.querySelector('input').focus();
}

function removeAchievement(button) {
    const container = document.getElementById('achievementsContainer');
    if (container.children.length > 1) {
        button.parentElement.remove();
    } else {
        // Clear the input instead of removing if it's the last one
        button.parentElement.querySelector('input').value = '';
    }
}

// Delete modal functions
function confirmDelete() {
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Auto-save draft functionality
let autoSaveTimer;
function autoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(function() {
        const formData = new FormData(document.getElementById('officialForm'));
        formData.append('auto_save', 'true');

        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(response => {
            if (response.ok) {
                console.log('Auto-saved');
            }
        }).catch(error => {
            console.error('Auto-save failed:', error);
        });
    }, 2000);
}

// Add auto-save to form inputs
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('input', autoSave);
        input.addEventListener('change', autoSave);
    });
});
</script>
{% endblock %}
