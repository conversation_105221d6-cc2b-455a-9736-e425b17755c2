{% extends 'base.html' %}
{% load static %}

{% block title %}Manage Officials - Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
    .official-card {
        transition: all 0.3s ease;
        border: 1px solid #e5e7eb;
    }

    .official-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        border-color: #3b82f6;
    }

    .profile-image {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #e5e7eb;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-active {
        background-color: #dcfce7;
        color: #166534;
    }

    .status-inactive {
        background-color: #fef3c7;
        color: #92400e;
    }

    .status-retired {
        background-color: #f3f4f6;
        color: #374151;
    }

    .position-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .position-mayor {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .position-vice_mayor {
        background-color: #dcfce7;
        color: #166534;
    }

    .position-councilor {
        background-color: #f3e8ff;
        color: #7c3aed;
    }

    .position-secretary {
        background-color: #fef3c7;
        color: #92400e;
    }

    .position-treasurer {
        background-color: #fecaca;
        color: #dc2626;
    }

    .filter-tabs {
        border-bottom: 1px solid #e5e7eb;
    }

    .filter-tab {
        padding: 0.75rem 1.5rem;
        border-bottom: 2px solid transparent;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .filter-tab.active {
        color: #3b82f6;
        border-bottom-color: #3b82f6;
    }

    .filter-tab:hover {
        color: #3b82f6;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-users text-blue-600 mr-3"></i>
                    Manage Officials
                </h1>
                <p class="text-gray-600 mt-2">
                    Manage municipal officials, their profiles, and information
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'ordinances:admin_dashboard' %}"
                   class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <a href="{% url 'ordinances:admin_official_create' %}"
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add New Official
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ stats.total }}</div>
            <div class="text-sm text-gray-600">Total Officials</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-green-600">{{ stats.active }}</div>
            <div class="text-sm text-gray-600">Active</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-yellow-600">{{ stats.inactive }}</div>
            <div class="text-sm text-gray-600">Inactive</div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
            <div class="text-2xl font-bold text-gray-600">{{ stats.retired }}</div>
            <div class="text-sm text-gray-600">Retired</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <!-- Filter Tabs -->
        <div class="filter-tabs">
            <div class="flex">
                <div class="filter-tab active" data-filter="all">
                    All Officials ({{ stats.total }})
                </div>
                <div class="filter-tab" data-filter="active">
                    Active ({{ stats.active }})
                </div>
                <div class="filter-tab" data-filter="inactive">
                    Inactive ({{ stats.inactive }})
                </div>
                <div class="filter-tab" data-filter="retired">
                    Retired ({{ stats.retired }})
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="p-6">
            <div class="flex flex-col md:flex-row gap-4">
                <!-- Search -->
                <div class="flex-1">
                    <div class="relative">
                        <input type="text"
                               id="searchInput"
                               placeholder="Search officials by name, position, or committee..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </div>

                <!-- Position Filter -->
                <div>
                    <select id="positionFilter"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Positions</option>
                        <option value="mayor">Mayor</option>
                        <option value="vice_mayor">Vice Mayor</option>
                        <option value="councilor">Council Members</option>
                        <option value="secretary">Secretary</option>
                        <option value="treasurer">Treasurer</option>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <select id="sortBy"
                            class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="order">Sort by Order</option>
                        <option value="name">Sort by Name</option>
                        <option value="position">Sort by Position</option>
                        <option value="term_start">Sort by Term Start</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Officials Grid -->
    <div id="officialsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for official in officials %}
            <div class="official-card bg-white rounded-lg shadow-sm p-6"
                 data-status="{{ official.status }}"
                 data-position="{{ official.position }}"
                 data-name="{{ official.name|lower }}"
                 data-committee="{{ official.committee|lower }}"
                 data-order="{{ official.order }}">

                <!-- Header -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <!-- Profile Picture -->
                        <div class="flex-shrink-0">
                            {% if official.profile_picture %}
                                <img src="{{ official.profile_picture.url }}"
                                     alt="{{ official.name }}"
                                     class="profile-image">
                            {% else %}
                                <div class="profile-image bg-gray-100 flex items-center justify-center">
                                    <i class="fas fa-user text-2xl text-gray-400"></i>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Basic Info -->
                        <div class="flex-1 min-w-0">
                            <h3 class="text-lg font-semibold text-gray-900 truncate">
                                {{ official.name }}
                            </h3>
                            <div class="position-badge position-{{ official.position }} mb-1">
                                {{ official.get_position_display }}
                            </div>
                            {% if official.committee %}
                                <p class="text-sm text-gray-600 truncate">{{ official.committee }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Status Badge -->
                    <div class="status-badge status-{{ official.status }}">
                        {{ official.get_status_display }}
                    </div>
                </div>

                <!-- Term Information -->
                <div class="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div class="text-sm text-gray-600">
                        <div class="flex justify-between">
                            <span>Term:</span>
                            <span class="font-medium">
                                {{ official.term_start|date:"M Y" }} - {{ official.term_end|date:"M Y" }}
                            </span>
                        </div>
                        {% if official.order %}
                            <div class="flex justify-between mt-1">
                                <span>Display Order:</span>
                                <span class="font-medium">#{{ official.order }}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Bio Preview -->
                {% if official.bio %}
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 line-clamp-3">
                            {{ official.bio|truncatewords:20 }}
                        </p>
                    </div>
                {% endif %}

                <!-- Achievements Count -->
                {% if official.get_achievements_list %}
                    <div class="mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                            {{ official.get_achievements_list|length }} Achievement{{ official.get_achievements_list|length|pluralize }}
                        </div>
                    </div>
                {% endif %}

                <!-- Actions -->
                <div class="flex space-x-2">
                    <a href="{% url 'ordinances:admin_official_edit' official.id %}"
                       class="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </a>
                    <a href="{% url 'ordinances:admin_official_view' official.id %}"
                       class="flex-1 bg-gray-600 text-white text-center py-2 px-3 rounded-lg hover:bg-gray-700 transition-colors text-sm">
                        <i class="fas fa-eye mr-1"></i>View
                    </a>
                </div>
            </div>
        {% empty %}
            <div class="col-span-full text-center py-12">
                <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">No Officials Found</h3>
                <p class="text-gray-500 mb-6">Start by adding your first municipal official.</p>
                <a href="{% url 'ordinances:admin_official_create' %}"
                   class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add First Official
                </a>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="mt-8 flex justify-center">
            <nav class="flex space-x-2">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}"
                       class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-3 py-2 bg-blue-600 text-white rounded-lg">{{ num }}</span>
                    {% else %}
                        <a href="?page={{ num }}"
                           class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                            {{ num }}
                        </a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}"
                       class="px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </nav>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const positionFilter = document.getElementById('positionFilter');
    const sortBy = document.getElementById('sortBy');
    const filterTabs = document.querySelectorAll('.filter-tab');
    const officialCards = document.querySelectorAll('.official-card');

    let currentStatusFilter = 'all';

    // Filter tabs
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Update active tab
            filterTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Update current filter
            currentStatusFilter = this.dataset.filter;

            // Apply filters
            applyFilters();
        });
    });

    // Search input
    searchInput.addEventListener('input', debounce(applyFilters, 300));

    // Position filter
    positionFilter.addEventListener('change', applyFilters);

    // Sort
    sortBy.addEventListener('change', applySorting);

    function applyFilters() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedPosition = positionFilter.value;

        officialCards.forEach(card => {
            const status = card.dataset.status;
            const position = card.dataset.position;
            const name = card.dataset.name;
            const committee = card.dataset.committee;

            let show = true;

            // Status filter
            if (currentStatusFilter !== 'all' && status !== currentStatusFilter) {
                show = false;
            }

            // Position filter
            if (selectedPosition && position !== selectedPosition) {
                show = false;
            }

            // Search filter
            if (searchTerm && !name.includes(searchTerm) && !committee.includes(searchTerm)) {
                show = false;
            }

            // Show/hide card
            if (show) {
                card.style.display = 'block';
                card.style.animation = 'fadeIn 0.3s ease';
            } else {
                card.style.display = 'none';
            }
        });

        // Update empty state
        updateEmptyState();
    }

    function applySorting() {
        const sortValue = sortBy.value;
        const grid = document.getElementById('officialsGrid');
        const cards = Array.from(officialCards);

        cards.sort((a, b) => {
            switch(sortValue) {
                case 'name':
                    return a.dataset.name.localeCompare(b.dataset.name);
                case 'position':
                    return a.dataset.position.localeCompare(b.dataset.position);
                case 'term_start':
                    // This would need term start data in dataset
                    return 0;
                case 'order':
                default:
                    return parseInt(a.dataset.order) - parseInt(b.dataset.order);
            }
        });

        // Re-append sorted cards
        cards.forEach(card => grid.appendChild(card));
    }

    function updateEmptyState() {
        const visibleCards = Array.from(officialCards).filter(card =>
            card.style.display !== 'none'
        );

        const existingEmptyState = document.getElementById('emptyState');

        if (visibleCards.length === 0 && !existingEmptyState) {
            const emptyState = document.createElement('div');
            emptyState.id = 'emptyState';
            emptyState.className = 'col-span-full text-center py-12';
            emptyState.innerHTML = `
                <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">No Officials Found</h3>
                <p class="text-gray-500 mb-6">Try adjusting your search or filter criteria.</p>
                <button onclick="clearFilters()"
                        class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-times mr-2"></i>Clear Filters
                </button>
            `;
            document.getElementById('officialsGrid').appendChild(emptyState);
        } else if (visibleCards.length > 0 && existingEmptyState) {
            existingEmptyState.remove();
        }
    }

    // Utility functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Global function for clear filters button
    window.clearFilters = function() {
        searchInput.value = '';
        positionFilter.value = '';
        sortBy.value = 'order';

        // Reset to "All" tab
        filterTabs.forEach(t => t.classList.remove('active'));
        filterTabs[0].classList.add('active');
        currentStatusFilter = 'all';

        applyFilters();
        applySorting();
    };

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();
        }

        // Escape to clear search
        if (e.key === 'Escape' && document.activeElement === searchInput) {
            searchInput.value = '';
            applyFilters();
        }
    });

    // Add fade-in animation CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
