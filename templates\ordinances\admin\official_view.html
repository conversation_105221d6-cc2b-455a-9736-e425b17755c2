{% extends 'base.html' %}
{% load static %}

{% block title %}{{ official.name }} - Official Details{% endblock %}

{% block extra_css %}
<style>
    .profile-image-large {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid #e5e7eb;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-active {
        background-color: #dcfce7;
        color: #166534;
    }
    
    .status-inactive {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .status-retired {
        background-color: #f3f4f6;
        color: #374151;
    }
    
    .position-badge {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-size: 1rem;
        font-weight: 600;
        text-align: center;
    }
    
    .position-mayor {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }
    
    .position-vice_mayor {
        background: linear-gradient(135deg, #10b981, #047857);
        color: white;
    }
    
    .position-councilor {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
    }
    
    .position-secretary {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }
    
    .position-treasurer {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }
    
    .info-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
    }
    
    .achievement-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background: #f8fafc;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        border-left: 4px solid #3b82f6;
    }
    
    .term-progress {
        background: #e5e7eb;
        border-radius: 9999px;
        height: 8px;
        overflow: hidden;
    }
    
    .term-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        border-radius: 9999px;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-user-tie text-blue-600 mr-3"></i>
                    Official Details
                </h1>
                <p class="text-gray-600 mt-2">
                    Complete information for {{ official.name }}
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'ordinances:admin_official_list' %}" 
                   class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to List
                </a>
                <a href="{% url 'ordinances:admin_official_edit' official.id %}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-edit mr-2"></i>Edit Official
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Section -->
        <div class="lg:col-span-1">
            <div class="info-card text-center">
                <!-- Profile Picture -->
                <div class="mb-6">
                    {% if official.profile_picture %}
                        <img src="{{ official.profile_picture.url }}" 
                             alt="{{ official.name }}" 
                             class="profile-image-large mx-auto">
                    {% else %}
                        <div class="profile-image-large mx-auto bg-gray-100 flex items-center justify-center">
                            <i class="fas fa-user text-6xl text-gray-400"></i>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Basic Info -->
                <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ official.name }}</h2>
                
                <!-- Position Badge -->
                <div class="position-badge position-{{ official.position }} mb-4">
                    {{ official.get_position_display }}
                </div>
                
                <!-- Status Badge -->
                <div class="status-badge status-{{ official.status }} inline-block mb-4">
                    {{ official.get_status_display }}
                </div>
                
                <!-- Committee -->
                {% if official.committee %}
                    <div class="text-gray-600 mb-4">
                        <i class="fas fa-building mr-2"></i>
                        {{ official.committee }}
                    </div>
                {% endif %}
                
                <!-- Display Order -->
                {% if official.order %}
                    <div class="text-sm text-gray-500">
                        Display Order: #{{ official.order }}
                    </div>
                {% endif %}
            </div>
            
            <!-- Quick Actions -->
            <div class="info-card mt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{% url 'ordinances:admin_official_edit' official.id %}" 
                       class="w-full bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors block">
                        <i class="fas fa-edit mr-2"></i>Edit Information
                    </a>
                    {% if official.profile_picture %}
                        <a href="{{ official.profile_picture.url }}" 
                           target="_blank"
                           class="w-full bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors block">
                            <i class="fas fa-download mr-2"></i>Download Photo
                        </a>
                    {% endif %}
                    <button onclick="printProfile()" 
                            class="w-full bg-purple-600 text-white text-center py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-print mr-2"></i>Print Profile
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Details Section -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Term Information -->
            <div class="info-card">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-calendar text-blue-600 mr-2"></i>
                    Term Information
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Term Start</label>
                        <div class="text-lg font-semibold text-gray-900">
                            {{ official.term_start|date:"F j, Y" }}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Term End</label>
                        <div class="text-lg font-semibold text-gray-900">
                            {{ official.term_end|date:"F j, Y" }}
                        </div>
                    </div>
                </div>
                
                <!-- Term Progress -->
                <div class="mt-6">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span>Term Progress</span>
                        <span id="termProgress">Calculating...</span>
                    </div>
                    <div class="term-progress">
                        <div class="term-progress-bar" id="termProgressBar" style="width: 0%"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>{{ official.term_start|date:"M Y" }}</span>
                        <span>{{ official.term_end|date:"M Y" }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Biography -->
            {% if official.bio %}
                <div class="info-card">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">
                        <i class="fas fa-file-alt text-green-600 mr-2"></i>
                        Biography
                    </h3>
                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        {{ official.bio|linebreaks }}
                    </div>
                </div>
            {% endif %}
            
            <!-- Achievements -->
            {% if official.get_achievements_list %}
                <div class="info-card">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">
                        <i class="fas fa-trophy text-yellow-600 mr-2"></i>
                        Key Achievements
                    </h3>
                    <div class="space-y-2">
                        {% for achievement in official.get_achievements_list %}
                            <div class="achievement-item">
                                <i class="fas fa-check-circle text-green-500 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">{{ achievement }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
            
            <!-- System Information -->
            <div class="info-card">
                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                    <i class="fas fa-info-circle text-gray-600 mr-2"></i>
                    System Information
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Created</label>
                        <div class="text-gray-900">
                            {{ official.created_at|date:"F j, Y g:i A" }}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                        <div class="text-gray-900">
                            {{ official.updated_at|date:"F j, Y g:i A" }}
                        </div>
                    </div>
                </div>
                
                {% if official.profile_picture %}
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Profile Picture</label>
                        <div class="text-sm text-gray-600">
                            <div>File: {{ official.profile_picture.name }}</div>
                            <div>Size: <span id="fileSize">Loading...</span></div>
                            <div>URL: <a href="{{ official.profile_picture.url }}" target="_blank" class="text-blue-600 hover:underline">{{ official.profile_picture.url }}</a></div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate term progress
    calculateTermProgress();
    
    // Get file size if profile picture exists
    {% if official.profile_picture %}
        getFileSize('{{ official.profile_picture.url }}');
    {% endif %}
});

function calculateTermProgress() {
    const termStart = new Date('{{ official.term_start|date:"Y-m-d" }}');
    const termEnd = new Date('{{ official.term_end|date:"Y-m-d" }}');
    const now = new Date();
    
    const totalDuration = termEnd - termStart;
    const elapsed = now - termStart;
    const progress = Math.max(0, Math.min(100, (elapsed / totalDuration) * 100));
    
    const progressBar = document.getElementById('termProgressBar');
    const progressText = document.getElementById('termProgress');
    
    if (progressBar && progressText) {
        progressBar.style.width = progress + '%';
        
        if (now < termStart) {
            progressText.textContent = 'Term not started';
            progressBar.style.background = '#6b7280';
        } else if (now > termEnd) {
            progressText.textContent = 'Term completed';
            progressBar.style.background = '#10b981';
        } else {
            progressText.textContent = Math.round(progress) + '% complete';
        }
    }
}

function getFileSize(url) {
    fetch(url, { method: 'HEAD' })
        .then(response => {
            const size = response.headers.get('content-length');
            if (size) {
                const sizeInMB = (parseInt(size) / (1024 * 1024)).toFixed(2);
                document.getElementById('fileSize').textContent = sizeInMB + ' MB';
            }
        })
        .catch(() => {
            document.getElementById('fileSize').textContent = 'Unknown';
        });
}

function printProfile() {
    const printWindow = window.open('', '_blank');
    const profileContent = `
        <html>
        <head>
            <title>{{ official.name }} - Official Profile</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .profile-img { width: 150px; height: 150px; border-radius: 50%; object-fit: cover; }
                .info-section { margin-bottom: 20px; }
                .info-section h3 { color: #333; border-bottom: 2px solid #3b82f6; padding-bottom: 5px; }
                .achievement { margin: 5px 0; padding-left: 20px; }
                .achievement:before { content: "✓ "; color: #10b981; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                {% if official.profile_picture %}
                    <img src="{{ official.profile_picture.url }}" alt="{{ official.name }}" class="profile-img">
                {% endif %}
                <h1>{{ official.name }}</h1>
                <h2>{{ official.get_position_display }}</h2>
                {% if official.committee %}
                    <p>{{ official.committee }}</p>
                {% endif %}
            </div>
            
            <div class="info-section">
                <h3>Term Information</h3>
                <p><strong>Term:</strong> {{ official.term_start|date:"F j, Y" }} - {{ official.term_end|date:"F j, Y" }}</p>
                <p><strong>Status:</strong> {{ official.get_status_display }}</p>
            </div>
            
            {% if official.bio %}
                <div class="info-section">
                    <h3>Biography</h3>
                    <p>{{ official.bio }}</p>
                </div>
            {% endif %}
            
            {% if official.get_achievements_list %}
                <div class="info-section">
                    <h3>Key Achievements</h3>
                    {% for achievement in official.get_achievements_list %}
                        <div class="achievement">{{ achievement }}</div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="info-section">
                <p><em>Generated on {{ "now"|date:"F j, Y g:i A" }}</em></p>
            </div>
        </body>
        </html>
    `;
    
    printWindow.document.write(profileContent);
    printWindow.document.close();
    printWindow.print();
}
</script>
{% endblock %}
