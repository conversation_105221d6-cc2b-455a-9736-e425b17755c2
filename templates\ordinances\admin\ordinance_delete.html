{% extends 'base.html' %}

{% block title %}Delete Ordinance - Admin{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
        </div>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Delete Ordinance</h1>
        <p class="text-lg text-gray-600">This action cannot be undone</p>
    </div>
    
    <!-- Ordinance Details -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Ordinance to be deleted:</h2>
        
        <div class="space-y-3">
            <div class="flex items-center justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Ordinance Number:</span>
                <span class="text-sm text-gray-900">{{ ordinance.ordinance_number }}</span>
            </div>
            
            <div class="flex items-start justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Title:</span>
                <span class="text-sm text-gray-900 text-right max-w-xs">{{ ordinance.title }}</span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Category:</span>
                <span class="text-sm text-gray-900">
                    {% if ordinance.category %}{{ ordinance.category.name }}{% else %}No category{% endif %}
                </span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Year:</span>
                <span class="text-sm text-gray-900">{{ ordinance.year_passed }}</span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Status:</span>
                <span class="text-sm">
                    {% if ordinance.status == 'published' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            Published
                        </span>
                    {% elif ordinance.status == 'approved' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Approved
                        </span>
                    {% elif ordinance.status == 'reviewed' %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            Reviewed
                        </span>
                    {% else %}
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                            Draft
                        </span>
                    {% endif %}
                </span>
            </div>
            
            <div class="flex items-center justify-between py-2 border-b border-gray-200">
                <span class="text-sm font-medium text-gray-600">Created:</span>
                <span class="text-sm text-gray-900">{{ ordinance.created_at|date:"F d, Y" }}</span>
            </div>
            
            {% if ordinance.sponsors.exists %}
                <div class="flex items-start justify-between py-2">
                    <span class="text-sm font-medium text-gray-600">Sponsors:</span>
                    <div class="text-sm text-gray-900 text-right max-w-xs">
                        {% for sponsor in ordinance.sponsors.all %}
                            {{ sponsor.name }}{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Warning -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Warning</h3>
                <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>This action will permanently delete the ordinance</li>
                        <li>All associated attachments will also be deleted</li>
                        <li>Activity logs related to this ordinance will be removed</li>
                        <li>This action cannot be undone</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{% url 'ordinances:admin_ordinance_list' %}" 
           class="bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-400 transition-colors text-center">
            Cancel
        </a>
        
        <a href="{% url 'ordinances:admin_ordinance_edit' ordinance.id %}" 
           class="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center">
            Edit Instead
        </a>
        
        <form method="post" class="inline">
            {% csrf_token %}
            <button type="submit" 
                    class="bg-red-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-red-700 transition-colors w-full"
                    onclick="return confirm('Are you absolutely sure you want to delete this ordinance? This action cannot be undone.')">
                🗑️ Delete Permanently
            </button>
        </form>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcut for cancel (Escape key)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            window.location.href = "{% url 'ordinances:admin_ordinance_list' %}";
        }
    });
});
</script>
{% endblock %}
{% endblock %}
