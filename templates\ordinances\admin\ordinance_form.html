{% extends 'base.html' %}

{% block title %}{% if ordinance %}Edit Ordinance{% else %}Create Ordinance{% endif %} - Admin{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-4">
                {% if ordinance %}Edit Ordinance{% else %}Create New Ordinance{% endif %}
            </h1>
            <p class="text-lg text-gray-600">
                {% if ordinance %}Update ordinance details and content{% else %}Add a new ordinance to the system{% endif %}
            </p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
            <a href="{% url 'ordinances:admin_ordinance_list' %}" 
               class="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors">
                ← Back to List
            </a>
            {% if ordinance %}
                <a href="{% url 'ordinances:admin_ordinance_delete' ordinance.id %}" 
                   class="bg-red-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-700 transition-colors"
                   onclick="return confirm('Are you sure you want to delete this ordinance?')">
                    🗑️ Delete
                </a>
            {% endif %}
        </div>
    </div>
    
    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="ordinance_number" class="block text-sm font-medium text-gray-700 mb-2">
                        Ordinance Number *
                    </label>
                    <input type="text" 
                           id="ordinance_number" 
                           name="ordinance_number" 
                           value="{% if ordinance %}{{ ordinance.ordinance_number }}{% endif %}"
                           required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="e.g., ORD-2024-001">
                </div>
                
                <div>
                    <label for="year_passed" class="block text-sm font-medium text-gray-700 mb-2">
                        Year Passed *
                    </label>
                    <select id="year_passed" 
                            name="year_passed" 
                            required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        {% for year in years %}
                            <option value="{{ year }}" {% if ordinance and ordinance.year_passed == year %}selected{% endif %}>
                                {{ year }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                </label>
                <input type="text" 
                       id="title" 
                       name="title" 
                       value="{% if ordinance %}{{ ordinance.title }}{% endif %}"
                       required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                       placeholder="Enter the ordinance title">
            </div>
            
            <!-- Category and Status -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category
                    </label>
                    <select id="category" 
                            name="category" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">Select a category</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" 
                                    {% if ordinance and ordinance.category_id == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        Status
                    </label>
                    <select id="status" 
                            name="status" 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" 
                                    {% if ordinance and ordinance.status == value %}selected{% elif not ordinance and value == 'draft' %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <!-- Sponsors -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Sponsors
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-300 rounded-lg p-4">
                    {% for sponsor in sponsors %}
                        <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded">
                            <input type="checkbox" 
                                   name="sponsors" 
                                   value="{{ sponsor.id }}"
                                   {% if ordinance and sponsor in ordinance.sponsors.all %}checked{% endif %}
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate">{{ sponsor.name }}</div>
                                <div class="text-xs text-gray-500">{{ sponsor.position }}</div>
                            </div>
                        </label>
                    {% empty %}
                        <p class="text-gray-500 text-sm col-span-full">No sponsors available. 
                           <a href="{% url 'ordinances:admin_sponsor_create' %}" class="text-blue-600 hover:text-blue-800">Add sponsors</a>
                        </p>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Content -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                    Content *
                </label>
                <textarea id="content" 
                          name="content" 
                          rows="15" 
                          required
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Enter the full ordinance content...">{% if ordinance %}{{ ordinance.content }}{% endif %}</textarea>
                <p class="mt-2 text-sm text-gray-500">
                    Enter the complete text of the ordinance. Use proper formatting and structure.
                </p>
            </div>
            
            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                <button type="submit" 
                        class="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    {% if ordinance %}Update Ordinance{% else %}Create Ordinance{% endif %}
                </button>
                
                <a href="{% url 'ordinances:admin_ordinance_list' %}" 
                   class="bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-400 transition-colors text-center">
                    Cancel
                </a>
                
                {% if ordinance %}
                    <div class="flex-1"></div>
                    <a href="{{ ordinance.get_absolute_url }}" 
                       target="_blank"
                       class="bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors text-center">
                        👁️ Preview
                    </a>
                {% endif %}
            </div>
        </form>
    </div>
    
    {% if ordinance %}
        <!-- Activity Log -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Activity Log</h3>
            <div class="space-y-3">
                {% for log in ordinance.logs.all|slice:":10" %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ log.action }}</div>
                            <div class="text-xs text-gray-500">{{ log.notes }}</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">{{ log.user.username }}</div>
                            <div class="text-xs text-gray-400">{{ log.timestamp|timesince }} ago</div>
                        </div>
                    </div>
                {% empty %}
                    <p class="text-gray-500 text-sm">No activity logs available</p>
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from title
    const titleInput = document.getElementById('title');
    const ordinanceNumberInput = document.getElementById('ordinance_number');
    
    // Auto-focus on first input
    if (ordinanceNumberInput && !ordinanceNumberInput.value) {
        ordinanceNumberInput.focus();
    }
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-red-500');
                isValid = false;
            } else {
                field.classList.remove('border-red-500');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
{% endblock %}
{% endblock %}
