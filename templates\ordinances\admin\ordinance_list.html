{% extends 'base.html' %}

{% block title %}Manage Ordinances - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Manage Ordinances</h1>
            <p class="text-lg text-gray-600">View, edit, and manage all ordinances</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{% url 'ordinances:admin_ordinance_create' %}"
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Add New Ordinance
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
         x-data="{ showAdvanced: false }">

        <form hx-get="{% url 'ordinances:admin_ordinance_list' %}"
              hx-target="#admin-ordinance-results"
              hx-indicator="#loading-indicator"
              hx-push-url="true"
              class="space-y-4">

            <div class="flex flex-col lg:flex-row gap-4">
                <div class="flex-1">
                    <input type="text"
                           name="search"
                           value="{{ search_query }}"
                           placeholder="Search by title, content, or ordinance number..."
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button type="submit"
                        class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    Search
                </button>
                <button type="button"
                        @click="showAdvanced = !showAdvanced"
                        class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                    <span x-text="showAdvanced ? 'Hide Filters' : 'Show Filters'"></span>
                </button>
            </div>

            <!-- Advanced Filters -->
            <div x-show="showAdvanced" x-cloak x-transition class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select name="category" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.slug }}" {% if category.slug == selected_category %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if value == selected_status %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </form>
    </div>

    <!-- Results -->
    <div id="admin-ordinance-results">
        {% include 'ordinances/admin/partials/ordinance_list.html' %}
    </div>
</div>

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form[hx-get]');
        const selects = form.querySelectorAll('select');

        selects.forEach(select => {
            select.addEventListener('change', function() {
                htmx.trigger(form, 'submit');
            });
        });

        // Search input debouncing
        const searchInput = form.querySelector('input[name="search"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                htmx.trigger(form, 'submit');
            }, 500);
        });
    });
</script>
{% endblock %}
{% endblock %}
