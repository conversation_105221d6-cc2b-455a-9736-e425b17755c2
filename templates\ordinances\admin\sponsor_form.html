{% extends 'base.html' %}

{% block title %}{% if sponsor %}Edit Sponsor{% else %}Create Sponsor{% endif %} - Admin{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">
            {% if sponsor %}Edit Sponsor{% else %}Add New Sponsor{% endif %}
        </h1>
        <p class="text-lg text-gray-600">
            {% if sponsor %}Update sponsor information{% else %}Add a new council member or sponsor{% endif %}
        </p>
    </div>
    
    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <!-- Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{% if sponsor %}{{ sponsor.name }}{% endif %}"
                       required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                       placeholder="e.g., Hon. Juan Dela Cruz">
                <p class="mt-2 text-sm text-gray-500">
                    Enter the full name of the council member or sponsor.
                </p>
            </div>
            
            <!-- Position -->
            <div>
                <label for="position" class="block text-sm font-medium text-gray-700 mb-2">
                    Position *
                </label>
                <input type="text" 
                       id="position" 
                       name="position" 
                       value="{% if sponsor %}{{ sponsor.position }}{% endif %}"
                       required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                       placeholder="e.g., Sangguniang Bayan Member">
                <p class="mt-2 text-sm text-gray-500">
                    Enter the official position or title.
                </p>
            </div>
            
            {% if sponsor %}
                <!-- Sponsor Stats -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Sponsor Statistics</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="text-2xl font-bold text-blue-600">{{ sponsor.ordinance.count }}</div>
                            <div class="text-sm text-gray-600">Total Ordinances</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-600">{{ sponsor.ordinance.filter.status='published'.count }}</div>
                            <div class="text-sm text-gray-600">Published</div>
                        </div>
                    </div>
                </div>
            {% endif %}
            
            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200">
                <button type="submit" 
                        class="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    {% if sponsor %}Update Sponsor{% else %}Create Sponsor{% endif %}
                </button>
                
                <a href="{% url 'ordinances:admin_sponsor_list' %}" 
                   class="bg-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-400 transition-colors text-center">
                    Cancel
                </a>
                
                {% if sponsor and sponsor.ordinance.count == 0 %}
                    <div class="flex-1"></div>
                    <a href="{% url 'ordinances:admin_sponsor_delete' sponsor.id %}" 
                       class="bg-red-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-red-700 transition-colors text-center"
                       onclick="return confirm('Are you sure you want to delete this sponsor?')">
                        🗑️ Delete
                    </a>
                {% endif %}
            </div>
        </form>
    </div>
    
    {% if sponsor and sponsor.ordinance.exists %}
        <!-- Related Ordinances -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Sponsored Ordinances</h3>
            <div class="space-y-3">
                {% for ordinance in sponsor.ordinance.all|slice:":5" %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ ordinance.ordinance_number }}</div>
                            <div class="text-sm text-gray-600">{{ ordinance.title|truncatechars:60 }}</div>
                        </div>
                        <div class="flex items-center space-x-2">
                            {% if ordinance.status == 'published' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    Published
                                </span>
                            {% elif ordinance.status == 'approved' %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Approved
                                </span>
                            {% else %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ ordinance.get_status_display }}
                                </span>
                            {% endif %}
                            <a href="{% url 'ordinances:admin_ordinance_edit' ordinance.id %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                Edit
                            </a>
                        </div>
                    </div>
                {% endfor %}
                
                {% if sponsor.ordinance.count > 5 %}
                    <div class="text-center pt-3">
                        <a href="{% url 'ordinances:admin_ordinance_list' %}?sponsor={{ sponsor.id }}" 
                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View all {{ sponsor.ordinance.count }} ordinances →
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on name input
    const nameInput = document.getElementById('name');
    if (nameInput && !nameInput.value) {
        nameInput.focus();
    }
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const nameField = document.getElementById('name');
        const positionField = document.getElementById('position');
        let isValid = true;
        
        if (!nameField.value.trim()) {
            nameField.classList.add('border-red-500');
            isValid = false;
        } else {
            nameField.classList.remove('border-red-500');
        }
        
        if (!positionField.value.trim()) {
            positionField.classList.add('border-red-500');
            isValid = false;
        } else {
            positionField.classList.remove('border-red-500');
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            if (!nameField.value.trim()) nameField.focus();
            else if (!positionField.value.trim()) positionField.focus();
        }
    });
});
</script>
{% endblock %}
{% endblock %}
