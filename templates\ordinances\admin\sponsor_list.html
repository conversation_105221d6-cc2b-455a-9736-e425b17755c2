{% extends 'base.html' %}

{% block title %}Manage Sponsors - Admin{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Manage Sponsors</h1>
            <p class="text-lg text-gray-600">Council members and ordinance sponsors</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
            <a href="{% url 'ordinances:admin_dashboard' %}" 
               class="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors">
                ← Back to Dashboard
            </a>
            <a href="{% url 'ordinances:admin_sponsor_create' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Add New Sponsor
            </a>
        </div>
    </div>
    
    <!-- Sponsors Grid -->
    {% if sponsors %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for sponsor in sponsors %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-blue-600 font-semibold text-lg">
                                    {{ sponsor.name|slice:":2"|upper }}
                                </span>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ sponsor.name }}</h3>
                                <p class="text-sm text-gray-600">{{ sponsor.position }}</p>
                            </div>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {{ sponsor.ordinance_count }} ordinance{{ sponsor.ordinance_count|pluralize }}
                        </span>
                    </div>
                    
                    <!-- Sponsor Stats -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
                            <span>Activity Level</span>
                            <span>{{ sponsor.ordinance_count }} sponsored</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" 
                                 style="width: {% if sponsors %}{% widthratio sponsor.ordinance_count sponsors.0.ordinance_count 100 %}%{% else %}0%{% endif %}"></div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex space-x-2">
                            <a href="{% url 'ordinances:admin_sponsor_edit' sponsor.id %}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                Edit
                            </a>
                            {% if sponsor.ordinance_count == 0 %}
                                <a href="{% url 'ordinances:admin_sponsor_delete' sponsor.id %}" 
                                   class="text-red-600 hover:text-red-800 text-sm font-medium"
                                   onclick="return confirm('Are you sure you want to delete this sponsor?')">
                                    Delete
                                </a>
                            {% else %}
                                <span class="text-gray-400 text-sm">Cannot delete (has ordinances)</span>
                            {% endif %}
                        </div>
                        
                        {% if sponsor.ordinance_count > 0 %}
                            <a href="{% url 'ordinances:admin_ordinance_list' %}?sponsor={{ sponsor.id }}" 
                               class="text-green-600 hover:text-green-800 text-sm font-medium">
                                View Ordinances →
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Summary Stats -->
        <div class="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Sponsor Statistics</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ sponsors.count }}</div>
                    <div class="text-sm text-gray-600">Total Sponsors</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">
                        {% for sponsor in sponsors %}{% if sponsor.ordinance_count > 0 %}1{% endif %}{% endfor %}
                    </div>
                    <div class="text-sm text-gray-600">Active Sponsors</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">
                        {% if sponsors %}{{ sponsors.0.ordinance_count }}{% else %}0{% endif %}
                    </div>
                    <div class="text-sm text-gray-600">Most Active</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-orange-600">
                        {% for sponsor in sponsors %}{{ sponsor.ordinance_count|add:0 }}{% endfor %}
                    </div>
                    <div class="text-sm text-gray-600">Total Sponsorships</div>
                </div>
            </div>
        </div>
        
        <!-- Position Breakdown -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">By Position</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% regroup sponsors by position as position_groups %}
                {% for position_group in position_groups %}
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">{{ position_group.grouper }}</h4>
                        <div class="text-2xl font-bold text-indigo-600 mb-1">{{ position_group.list|length }}</div>
                        <div class="text-sm text-gray-600">member{{ position_group.list|length|pluralize }}</div>
                        <div class="mt-2 space-y-1">
                            {% for sponsor in position_group.list|slice:":3" %}
                                <div class="text-xs text-gray-500">{{ sponsor.name }}</div>
                            {% endfor %}
                            {% if position_group.list|length > 3 %}
                                <div class="text-xs text-gray-400">+{{ position_group.list|length|add:"-3" }} more</div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
        
    {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 mb-4">
                <svg class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No sponsors yet</h3>
            <p class="text-gray-600 mb-6">Get started by adding council members and other ordinance sponsors.</p>
            <a href="{% url 'ordinances:admin_sponsor_create' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Add First Sponsor
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
