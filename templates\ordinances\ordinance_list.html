{% extends 'base.html' %}

{% block title %}Ordinances - Sangguniang Bayan Ordinance System{% endblock %}

{# Professional contained layout for ordinance browsing - not too wide, maintains focus #}
{% block container_classes %}max-w-6xl mx-auto px-4 sm:px-6 lg:px-8{% endblock %}
{% block main_classes %}bg-gray-50 py-8{% endblock %}

{% block content %}
<div class="py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Municipal Ordinances</h1>
        <p class="text-lg text-gray-600">Browse and search through our collection of approved ordinances</p>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
         x-data="{ showAdvanced: false }">

        <!-- Main Search -->
        <form hx-get="{% url 'ordinances:ordinance_list' %}"
              hx-target="#ordinance-results"
              hx-indicator="#loading-indicator"
              hx-push-url="true"
              class="space-y-4">

            <div class="flex flex-col lg:flex-row gap-4">
                <div class="flex-1">
                    <input type="text"
                           name="search"
                           value="{{ search_query }}"
                           placeholder="Search by title, content, or ordinance number..."
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <button type="submit"
                        class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    Search
                </button>
                <button type="button"
                        @click="showAdvanced = !showAdvanced"
                        class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                    <span x-text="showAdvanced ? 'Hide Filters' : 'Show Filters'"></span>
                </button>
            </div>

            <!-- Advanced Filters -->
            <div x-show="showAdvanced" x-cloak x-transition class="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select name="category" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.slug }}" {% if category.slug == selected_category %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
                    <select name="year" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">All Years</option>
                        {% for year in years %}
                            <option value="{{ year }}" {% if year|stringformat:"s" == selected_year %}selected{% endif %}>
                                {{ year }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        <option value="approved" {% if selected_status == 'approved' %}selected{% endif %}>Approved</option>
                        <option value="published" {% if selected_status == 'published' %}selected{% endif %}>Published</option>
                    </select>
                </div>
            </div>
        </form>
    </div>

    <!-- Results -->
    <div id="ordinance-results">
        {% include 'ordinances/partials/ordinance_list.html' %}
    </div>
</div>

{% block extra_js %}
<script>
    // Auto-submit form on filter change
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form[hx-get]');
        const selects = form.querySelectorAll('select');

        selects.forEach(select => {
            select.addEventListener('change', function() {
                htmx.trigger(form, 'submit');
            });
        });

        // Search input debouncing
        const searchInput = form.querySelector('input[name="search"]');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                htmx.trigger(form, 'submit');
            }, 500);
        });
    });
</script>
{% endblock %}
{% endblock %}
