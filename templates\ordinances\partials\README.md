# Ordinances Home Page - Modular Template Structure

This document explains the modular template structure implemented for the ordinances home page to improve maintainability, reusability, and organization.

## Overview

The home page has been refactored from a single large template file into multiple smaller, focused components. This modular approach provides several benefits:

- **Maintainability**: Each section can be edited independently
- **Reusability**: Components can be reused across different pages
- **Organization**: Clear separation of concerns
- **Collaboration**: Multiple developers can work on different sections simultaneously
- **Testing**: Individual components can be tested in isolation

## File Structure

```
templates/ordinances/
├── home.html                          # Main template (40 lines)
└── partials/
    ├── README.md                      # This documentation
    ├── banner_section.html            # Dynamic announcement banners
    ├── hero_section.html              # Main header with statistics
    ├── officials_section.html         # Municipal officials display
    ├── official_card.html             # Individual official card component
    ├── council_member_card.html       # Council member card component
    ├── search_section.html            # Ordinance search functionality
    ├── recent_ordinances_section.html # Latest ordinances display
    ├── ordinance_card.html            # Individual ordinance card component
    ├── notices_section.html           # Important notices and announcements
    ├── notice_card.html               # Individual notice card component
    └── footer_section.html            # Official footer with contact info
```

## Component Descriptions

### 1. **banner_section.html**
- **Purpose**: Displays admin-controlled announcement banners
- **Features**:
  - Dynamic banner loading from database
  - Individual dismiss functionality
  - Admin management controls
- **Dependencies**: Banner model, active_banners context variable

### 2. **hero_section.html**
- **Purpose**: Main landing section with title, statistics, and call-to-action buttons
- **Features**:
  - Animated statistics counters
  - Responsive design
  - AOS animations
- **Dependencies**: total_ordinances, total_categories, top_sponsors context variables

### 3. **officials_section.html**
- **Purpose**: Displays municipal officials (Mayor, Vice Mayor, Council Members)
- **Features**:
  - Responsive grid layout
  - Includes sub-components for individual officials
- **Dependencies**: officials context variable, official_card.html, council_member_card.html

### 4. **official_card.html**
- **Purpose**: Reusable component for displaying individual officials (Mayor/Vice Mayor)
- **Parameters**:
  - `official`: Official object
  - `position_type`: 'mayor' or 'vice_mayor'
  - `position_label`: Display label
  - `position_icon`: Font Awesome icon class
  - `aos_direction`: Animation direction
  - `aos_delay`: Animation delay
- **Features**: Profile pictures, bio, achievements, responsive design

### 5. **council_member_card.html**
- **Purpose**: Simplified card for council members
- **Parameters**:
  - `member`: Council member object
- **Features**: Compact design, committee information

### 6. **search_section.html**
- **Purpose**: Ordinance search functionality
- **Features**:
  - Main search input
  - Category, year, and status filters
  - Responsive form layout
- **Dependencies**: categories context variable

### 7. **recent_ordinances_section.html**
- **Purpose**: Displays latest ordinances
- **Features**:
  - Grid layout for ordinance cards
  - "View All" link
  - Empty state handling
- **Dependencies**: recent_ordinances context variable, ordinance_card.html

### 8. **ordinance_card.html**
- **Purpose**: Reusable component for displaying individual ordinances
- **Parameters**:
  - `ordinance`: Ordinance object
- **Features**: Ordinance number, title, content preview, category, read more link

### 9. **notices_section.html**
- **Purpose**: Important notices and announcements
- **Features**:
  - Statistics cards for various metrics
  - Important notice banner
  - Responsive grid layout
- **Dependencies**: notice_card.html

### 10. **notice_card.html**
- **Purpose**: Reusable component for notice/announcement cards
- **Parameters**:
  - `icon`: Font Awesome icon class
  - `title`: Card title
  - `subtitle`: Optional subtitle
  - `count`: Statistic or count to display
  - `description`: Card description
  - `link_url`: URL for the action link
  - `link_params`: Optional URL parameters
  - `link_text`: Link text
  - `aos_delay`: Animation delay
- **Features**: Flexible content structure, hover effects

### 11. **footer_section.html**
- **Purpose**: Official footer with municipal information
- **Features**:
  - Official seal and branding
  - Contact information
  - Mission statement
  - Copyright information

## Usage Examples

### Including a Section
```django
<!-- Include a complete section -->
{% include 'ordinances/partials/hero_section.html' %}
```

### Including a Component with Parameters
```django
<!-- Include official card with parameters -->
{% include 'ordinances/partials/official_card.html' with official=officials.mayor position_type='mayor' position_label='Mayor' position_icon='fas fa-crown' aos_direction='fade-right' aos_delay='200' %}
```

### Including in a Loop
```django
<!-- Include ordinance cards in a loop -->
{% for ordinance in recent_ordinances %}
    {% include 'ordinances/partials/ordinance_card.html' with ordinance=ordinance %}
{% endfor %}
```

## Context Variables Required

The modular templates expect the following context variables to be passed from the view:

```python
context = {
    # Banner system
    'active_banners': Banner.get_active_banners(),

    # Statistics
    'total_ordinances': total_ordinances,
    'total_categories': total_categories,
    'top_sponsors': top_sponsors,

    # Officials
    'officials': {
        'mayor': mayor_object,
        'vice_mayor': vice_mayor_object,
        'council_members': council_members_queryset,
    },

    # Content
    'categories': categories_queryset,
    'recent_ordinances': recent_ordinances_queryset,

    # Date information for notices section
    'current_year': current_year,
    'current_month': current_month,

    # Metrics for notices section
    'new_ordinances_count': new_count,
    'updated_ordinances_count': updated_count,
}
```

### Required Context Variables by Component:

- **banner_section.html**: `active_banners`
- **hero_section.html**: `total_ordinances`, `total_categories`, `top_sponsors`
- **officials_section.html**: `officials`
- **search_section.html**: `categories`
- **recent_ordinances_section.html**: `recent_ordinances`
- **notices_section.html**: `new_ordinances_count`, `updated_ordinances_count`, `current_year`, `current_month`
- **footer_section.html**: No specific context variables required

## Benefits of This Structure

1. **Maintainability**: Each component is focused on a single responsibility
2. **Reusability**: Components can be used in other templates
3. **Testability**: Individual components can be tested separately
4. **Performance**: Easier to optimize specific sections
5. **Collaboration**: Multiple developers can work on different components
6. **Consistency**: Standardized component structure across the application

## Best Practices

1. **Naming Convention**: Use descriptive names ending with `_section.html` for major sections and `_card.html` for reusable components
2. **Documentation**: Document component parameters and dependencies
3. **Context Variables**: Keep context variable names consistent across components
4. **Responsive Design**: Ensure all components work across different screen sizes
5. **Accessibility**: Include proper ARIA labels and semantic HTML
6. **Performance**: Minimize database queries within templates

## Future Enhancements

1. **Component Library**: Create a comprehensive component library documentation
2. **Automated Testing**: Implement template testing for each component
3. **Style Guide**: Develop a consistent styling guide for components
4. **Internationalization**: Add translation support to components
5. **Caching**: Implement template fragment caching for performance optimization
