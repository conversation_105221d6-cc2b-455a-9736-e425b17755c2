<!-- Dynamic Announcement Banners -->
{% for banner in active_banners %}
<div id="banner-{{ banner.id }}" class="{{ banner.get_css_classes }} py-2 relative border-b-2" data-banner-id="{{ banner.id }}">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2 flex-1">
                {% if banner.show_icon %}
                    <i class="{{ banner.get_icon_class }}"></i>
                {% endif %}
                <div class="banner-content">
                    <span class="font-semibold text-sm">{{ banner.title }}:</span>
                    <span class="ml-2 text-sm">{{ banner.message }}</span>
                </div>
            </div>
            {% if banner.is_dismissible %}
            <button onclick="dismissBanner({{ banner.id }})" class="hover:opacity-75 transition-opacity ml-4 p-1">
                <i class="fas fa-times text-sm"></i>
            </button>
            {% endif %}
        </div>
    </div>
</div>
{% endfor %}

<!-- Admin Controls for Banner Management -->
{% if user.is_authenticated %}
<div class="fixed bottom-4 right-4 z-50 space-y-2">
    <a href="{% url 'ordinances:admin_banner_list' %}" class="bg-blue-600 text-white px-3 py-2 rounded text-xs hover:bg-blue-700 block text-center">
        Manage Banners
    </a>
</div>
{% endif %}
