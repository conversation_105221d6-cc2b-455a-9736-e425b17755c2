{% load static %}

{% if official %}
    <div class="official-card bg-white rounded-3xl shadow-2xl overflow-hidden border border-primary-beige/30 hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2" data-aos="{{ aos_direction }}" data-aos-delay="{{ aos_delay }}">
        <div class="relative">
            <!-- Enhanced <PERSON> with <PERSON><PERSON> Gradient -->
            <div class="h-64 bg-gradient-to-br {% if position_type == 'mayor' %}from-blue-500 via-blue-600 to-primary-dark-blue{% else %}from-primary-beige via-blue-200 to-blue-400{% endif %} flex items-center justify-center relative overflow-hidden">
                <!-- Decorative Pattern Overlay -->
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                    <div class="absolute top-0 left-0 w-full h-full">
                        <div class="absolute top-4 left-4 w-8 h-8 border-2 border-white/30 rounded-full"></div>
                        <div class="absolute top-8 right-8 w-6 h-6 border-2 border-white/20 rounded-full"></div>
                        <div class="absolute bottom-6 left-8 w-4 h-4 border-2 border-white/25 rounded-full"></div>
                    </div>
                </div>

                <!-- Logo Watermark -->
                <div class="absolute inset-0 flex items-center justify-center opacity-15">
                    <img src="{% static 'img/dumingag-logo.png' %}"
                         alt="Dumingag Logo"
                         class="w-40 h-40 object-contain">
                </div>

                <!-- Profile Picture Container -->
                <div class="w-36 h-36 bg-white rounded-full flex items-center justify-center relative z-10 overflow-hidden shadow-2xl border-4 border-white/50">
                    {% if official.profile_picture %}
                        <img src="{{ official.profile_picture.url }}"
                             alt="{{ official.name }}"
                             class="w-full h-full object-cover">
                    {% else %}
                        <i class="fas fa-user-tie text-6xl text-primary-dark-blue"></i>
                    {% endif %}
                </div>
            </div>

            <!-- Enhanced Position Badge -->
            <div class="absolute top-4 right-4 bg-white/95 backdrop-blur-sm text-primary-dark-blue px-4 py-2 rounded-full text-sm font-bold shadow-lg border border-primary-beige/50">
                <i class="{{ position_icon }} mr-2 text-blue-500"></i>{{ position_label }}
            </div>
        </div>
        <!-- Enhanced Content Area -->
        <div class="p-8 bg-gradient-to-b from-white to-gray-50/50">
            <!-- Name and Title -->
            <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-primary-dark-blue mb-2 leading-tight">{{ official.name }}</h3>
                <div class="inline-flex items-center px-4 py-2 bg-blue-50 rounded-full border border-blue-200">
                    <i class="fas fa-badge-check text-blue-500 mr-2"></i>
                    <p class="text-primary-dark-blue font-semibold text-sm">{{ official.get_position_display }}</p>
                </div>
            </div>

            {% if official.bio %}
                <div class="mb-6 p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                    <p class="text-gray-700 text-sm leading-relaxed italic">{{ official.bio }}</p>
                </div>
            {% endif %}

            {% if official.get_achievements_list %}
                <div class="space-y-3">
                    <h4 class="font-bold text-primary-dark-blue flex items-center">
                        <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                        Key Achievements
                    </h4>
                    <div class="space-y-2">
                        {% for achievement in official.get_achievements_list %}
                            <div class="flex items-start text-sm text-gray-700 p-3 bg-green-50 rounded-lg border-l-4 border-green-400">
                                <i class="fas fa-check-circle text-green-500 mr-3 mt-0.5 flex-shrink-0"></i>
                                <span class="leading-relaxed">{{ achievement }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% else %}
    <!-- Enhanced Empty State Card -->
    <div class="bg-white rounded-3xl shadow-xl p-8 text-center border-2 border-dashed border-blue-200 hover:border-blue-300 transition-all duration-300 hover:shadow-2xl" data-aos="{{ aos_direction }}" data-aos-delay="{{ aos_delay }}">
        <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-user-plus text-3xl text-blue-500"></i>
        </div>
        <h3 class="text-xl font-bold text-primary-dark-blue mb-3">{{ position_label }} Position</h3>
        <p class="text-gray-500 text-sm mb-4">No {{ position_label|lower }} information available</p>
        <div class="inline-flex items-center px-4 py-2 bg-blue-50 rounded-full border border-blue-200">
            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
            <span class="text-blue-700 text-xs font-medium">Position Available</span>
        </div>
    </div>
{% endif %}
