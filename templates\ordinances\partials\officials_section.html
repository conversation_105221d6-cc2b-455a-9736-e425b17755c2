<!-- Officials Section -->
<section id="officials" class="py-20 bg-gradient-to-br from-primary-offwhite to-primary-beige">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <!-- Section Header with Logo -->
            <div class="flex items-center justify-center mb-6">
                <h2 class="text-4xl font-bold text-primary-dark-blue">
                    Our Municipal Leaders
                </h2>
            </div>
            <p class="text-xl text-primary-black max-w-3xl mx-auto">
                Meet the dedicated officials serving the Municipality of Dumingag with integrity and commitment to public service.
            </p>
        </div>

        <!-- Mayor and Vice Mayor -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <!-- Mayor -->
            {% include 'ordinances/partials/official_card.html' with official=officials.mayor position_type='mayor' position_label='Mayor' position_icon='fas fa-crown' aos_direction='fade-right' aos_delay='200' %}

            <!-- Vice Mayor -->
            {% include 'ordinances/partials/official_card.html' with official=officials.vice_mayor position_type='vice_mayor' position_label='Vice Mayor' position_icon='fas fa-star' aos_direction='fade-left' aos_delay='400' %}
        </div>

        <!-- Council Members -->
        <div class="mb-12" data-aos="fade-up" data-aos-delay="600">
            <h3 class="text-3xl font-bold text-center text-primary-dark-blue mb-8">
                <i class="fas fa-users text-primary-dark-blue mr-3 text-4xl"></i>
                Sangguniang Bayan Members
            </h3>
            {% if officials.council_members %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {% for member in officials.council_members %}
                        {% include 'ordinances/partials/council_member_card.html' with member=member %}
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12 bg-white/50 rounded-2xl border-2 border-primary-dark-blue/20 shadow-lg">
                    <i class="fas fa-users text-6xl text-primary-dark-blue mb-4 opacity-60"></i>
                    <h4 class="text-xl font-bold text-primary-dark-blue mb-2">No Council Members</h4>
                    <p class="text-primary-black/70">Council member information will be displayed here when available.</p>
                </div>
            {% endif %}
        </div>
    </div>
</section>
