<!-- Results Summary -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
    <div class="text-sm text-gray-700">
        {% if page_obj.paginator.count %}
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} ordinances
        {% else %}
            No ordinances found
        {% endif %}
    </div>

    {% if search_query or selected_category or selected_year or selected_status %}
        <div class="mt-2 sm:mt-0">
            <a href="{% url 'ordinances:ordinance_list' %}"
               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                Clear all filters
            </a>
        </div>
    {% endif %}
</div>

<!-- Active Filters -->
{% if search_query or selected_category or selected_year or selected_status %}
    <div class="mb-6">
        <div class="flex flex-wrap gap-2">
            {% if search_query %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                    Search: "{{ search_query }}"
                    <button type="button"
                            onclick="document.querySelector('input[name=search]').value=''; htmx.trigger(document.querySelector('form[hx-get]'), 'submit');"
                            class="ml-2 text-blue-600 hover:text-blue-800">
                        ×
                    </button>
                </span>
            {% endif %}

            {% if selected_category %}
                {% for category in categories %}
                    {% if category.slug == selected_category %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
                            Category: {{ category.name }}
                            <button type="button"
                                    onclick="document.querySelector('select[name=category]').value=''; htmx.trigger(document.querySelector('form[hx-get]'), 'submit');"
                                    class="ml-2 text-green-600 hover:text-green-800">
                                ×
                            </button>
                        </span>
                    {% endif %}
                {% endfor %}
            {% endif %}

            {% if selected_year %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-purple-100 text-purple-800">
                    Year: {{ selected_year }}
                    <button type="button"
                            onclick="document.querySelector('select[name=year]').value=''; htmx.trigger(document.querySelector('form[hx-get]'), 'submit');"
                            class="ml-2 text-purple-600 hover:text-purple-800">
                        ×
                    </button>
                </span>
            {% endif %}

            {% if selected_status %}
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-orange-100 text-orange-800">
                    Status: {{ selected_status|title }}
                    <button type="button"
                            onclick="document.querySelector('select[name=status]').value=''; htmx.trigger(document.querySelector('form[hx-get]'), 'submit');"
                            class="ml-2 text-orange-600 hover:text-orange-800">
                        ×
                    </button>
                </span>
            {% endif %}
        </div>
    </div>
{% endif %}

<!-- Ordinances Grid -->
{% if page_obj.object_list %}
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {% for ordinance in page_obj.object_list %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <div class="p-6">
                    <!-- Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <div class="flex items-center gap-2 mb-2">
                                <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                    {{ ordinance.ordinance_number }}
                                </span>
                                <span class="bg-gray-100 text-gray-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                    {{ ordinance.year_passed }}
                                </span>
                                {% if ordinance.status == 'published' %}
                                    <span class="bg-green-100 text-green-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                        Published
                                    </span>
                                {% elif ordinance.status == 'approved' %}
                                    <span class="bg-yellow-100 text-yellow-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                        Approved
                                    </span>
                                {% endif %}
                            </div>

                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                {{ ordinance.title }}
                            </h3>
                        </div>
                    </div>

                    <!-- Content Preview -->
                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                        {{ ordinance.content|truncatewords:25 }}
                    </p>

                    <!-- Metadata -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <div class="flex items-center gap-4">
                            {% if ordinance.category %}
                                <span class="flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                    </svg>
                                    {{ ordinance.category.name }}
                                </span>
                            {% endif %}

                            {% if ordinance.sponsors.exists %}
                                <span class="flex items-center">
                                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    {{ ordinance.sponsors.count }} sponsor{{ ordinance.sponsors.count|pluralize }}
                                </span>
                            {% endif %}
                        </div>

                        <span>{{ ordinance.created_at|date:"M d, Y" }}</span>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-between">
                        <a href="{{ ordinance.get_absolute_url }}"
                           class="text-blue-600 hover:text-blue-800 font-medium text-sm flex items-center">
                            Read Full Text
                            <svg class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </a>

                        <a href="{% url 'ordinances:export_pdf' ordinance.slug %}"
                           class="text-gray-600 hover:text-gray-800 text-sm flex items-center"
                           title="Download PDF">
                            <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            PDF
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg">
            <div class="flex flex-1 justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                       hx-get="{% url 'ordinances:ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                       hx-target="#ordinance-results"
                       hx-push-url="true"
                       class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}

                {% if page_obj.has_next %}
                    <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                       hx-get="{% url 'ordinances:ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                       hx-target="#ordinance-results"
                       hx-push-url="true"
                       class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>

            <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </p>
                </div>

                <div>
                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                               hx-get="{% url 'ordinances:ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                               hx-target="#ordinance-results"
                               hx-push-url="true"
                               class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative z-10 inline-flex items-center bg-blue-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ num }}"
                                   hx-get="{% url 'ordinances:ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ num }}"
                                   hx-target="#ordinance-results"
                                   hx-push-url="true"
                                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                               hx-get="{% url 'ordinances:ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_year %}year={{ selected_year }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                               hx-target="#ordinance-results"
                               hx-push-url="true"
                               class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}

{% else %}
    <!-- No Results -->
    <div class="text-center py-12">
        <div class="text-gray-400 mb-4">
            <svg class="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No ordinances found</h3>
        <p class="text-gray-600 mb-4">Try adjusting your search criteria or browse all ordinances.</p>
        <a href="{% url 'ordinances:ordinance_list' %}"
           class="text-blue-600 hover:text-blue-800 font-medium">
            Clear all filters
        </a>
    </div>
{% endif %}
