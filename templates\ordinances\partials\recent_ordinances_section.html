<!-- Recent Ordinances -->
<div class="bg-primary-beige py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-primary-dark-blue mb-4">Recent Ordinances</h2>
            <p class="text-lg text-primary-black">Latest approved and published ordinances</p>
        </div>

        {% if recent_ordinances %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for ordinance in recent_ordinances %}
                    {% include 'ordinances/partials/ordinance_card.html' with ordinance=ordinance %}
                {% endfor %}
            </div>

            <div class="text-center mt-12">
                <a href="{% url 'ordinances:ordinance_list' %}"
                   class="btn-primary px-8 py-3 rounded-lg font-semibold transition-colors">
                    View All Ordinances
                </a>
            </div>
        {% else %}
            <div class="text-center py-12 bg-white/50 rounded-2xl border-2 border-primary-dark-blue/20 shadow-lg">
                <div class="text-primary-dark-blue mb-4 opacity-60">
                    <svg class="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-primary-dark-blue mb-2">No ordinances available</h3>
                <p class="text-primary-black/70">Check back later for new ordinances.</p>
            </div>
        {% endif %}
    </div>
</div>
