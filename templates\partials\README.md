# 📁 Global Template Partials

This directory contains reusable template components that are used across the entire application, particularly in the base template structure.

## 🧩 **Components Overview**

### **1. `head.html`**
**Purpose**: Contains all HTML head elements including meta tags, scripts, and stylesheets.

**Includes**:
- Meta tags (charset, viewport)
- TailwindCSS configuration with custom colors
- HTMX for dynamic interactions
- Alpine.js for reactive components
- Font Awesome icons
- Custom CSS
- Favicon
- Block for additional head content

**Usage**: 
```html
{% include 'partials/head.html' %}
```

### **2. `navigation.html`**
**Purpose**: Complete navigation component with responsive design.

**Features**:
- Municipality branding with logo
- Desktop and mobile navigation
- Authentication-aware menu items
- Responsive hamburger menu
- Alpine.js powered mobile toggle

**Usage**:
```html
{% include 'partials/navigation.html' %}
```

### **3. `messages.html`**
**Purpose**: Django messages display with enhanced styling.

**Features**:
- Support for all Django message types (success, error, warning, info)
- Font Awesome icons for each message type
- Alpine.js powered dismissible messages
- Smooth transitions
- Responsive design

**Usage**:
```html
{% include 'partials/messages.html' %}
```

### **4. `loading_indicator.html`**
**Purpose**: HTMX loading indicator for async operations.

**Features**:
- Animated spinner
- Fixed positioning (top-right)
- Automatic show/hide with HTMX requests
- Professional styling

**Usage**:
```html
{% include 'partials/loading_indicator.html' %}
```

### **5. `scripts.html`**
**Purpose**: Base JavaScript includes and script blocks.

**Features**:
- Base JavaScript file inclusion
- Block for additional JavaScript
- Proper loading order

**Usage**:
```html
{% include 'partials/scripts.html' %}
```

## 🏗️ **Modular Architecture Benefits**

### **✅ Maintainability**
- Each component has a single responsibility
- Easy to update individual components
- Reduced code duplication

### **✅ Reusability**
- Components can be used across different templates
- Consistent styling and behavior
- DRY (Don't Repeat Yourself) principle

### **✅ Scalability**
- Easy to add new components
- Simple to modify existing components
- Better organization for large projects

### **✅ Testing**
- Individual components can be tested separately
- Easier to isolate issues
- Better debugging experience

## 📂 **Directory Structure**

```
templates/
├── base.html                    # Main base template (now modular)
├── partials/                    # Global reusable components
│   ├── head.html               # HTML head content
│   ├── navigation.html         # Main navigation
│   ├── messages.html           # Django messages
│   ├── loading_indicator.html  # HTMX loading spinner
│   ├── scripts.html           # JavaScript includes
│   └── README.md              # This documentation
└── ordinances/
    ├── partials/              # App-specific components
    │   ├── hero_section.html
    │   ├── footer_section.html
    │   └── ...
    └── ...
```

## 🔧 **Usage Guidelines**

### **When to Create a New Partial**
1. **Reusable Component**: Used in multiple templates
2. **Complex Section**: Large block of HTML that clutters the main template
3. **Conditional Content**: Content that may be shown/hidden based on conditions
4. **Maintainability**: Component that may need frequent updates

### **Naming Conventions**
- Use descriptive names: `navigation.html`, not `nav.html`
- Use underscores for multi-word names: `loading_indicator.html`
- Include the component type: `_section.html`, `_card.html`, `_form.html`

### **Best Practices**
1. **Keep components focused**: One responsibility per component
2. **Use proper comments**: Document complex components
3. **Include context variables**: Document required variables in comments
4. **Test thoroughly**: Ensure components work in different contexts

## 🚀 **Future Enhancements**

- **Component Library**: Create a visual component library
- **Documentation**: Add inline documentation for complex components
- **Testing**: Implement component-level testing
- **Performance**: Optimize component loading and rendering
