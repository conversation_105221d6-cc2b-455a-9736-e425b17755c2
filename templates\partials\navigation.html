{% load static %}

<!--
SECURITY IMPLEMENTATION:
- Admin links are only visible to authenticated staff users (user.is_authenticated and user.is_staff)
- Public users see only public navigation links (Home, Ordinances, Contact)
- Admin access is available via dedicated URL: /admin-access/
- No admin login links are exposed in public navigation for security
-->

<!-- Main Navigation -->
<nav class="nav-primary shadow-lg relative" id="main-navigation">
    <!-- Background Overlay -->
    <div class="absolute inset-0 bg-gradient-to-r from-primary-dark-blue/90 via-primary-dark-blue/85 to-primary-dark-blue/90"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="flex justify-between h-16">
            <!-- Logo and Brand -->
            <div class="flex items-center">
                <a href="{% url 'ordinances:home' %}" class="flex-shrink-0 flex items-center group">
                    <div class="w-10 h-10 mr-3 transition-transform duration-300 group-hover:scale-110">
                        <img src="{% static 'img/dumingag-logo.png' %}"
                             alt="Dumingag Logo"
                             class="w-full h-full object-contain">
                    </div>
                    <div class="transition-all duration-300 group-hover:translate-x-1">
                        <h1 class="text-primary-offwhite text-lg font-bold leading-tight">Municipality of Dumingag</h1>
                        <p class="text-primary-beige text-xs">Public Ordinances Portal</p>
                    </div>
                </a>

                <!-- Desktop Navigation Links -->
                <div class="hidden md:ml-6 md:flex md:space-x-8">
                    <a href="{% url 'ordinances:home' %}"
                       class="nav-link px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                        Home
                    </a>
                    <a href="{% url 'ordinances:ordinance_list' %}"
                       class="nav-link px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                        Ordinances
                    </a>
                </div>
            </div>

            <!-- Desktop Auth Links - Only for Authenticated Admin Users -->
            <div class="hidden md:flex md:items-center md:space-x-4">
                {% if user.is_authenticated and user.is_staff %}
                    <!-- Admin User Menu -->
                    <div class="relative dropdown-container">
                        <button onclick="toggleAdminDropdown()"
                                class="nav-link px-3 py-2 rounded-md text-sm font-medium flex items-center transition-colors duration-200"
                                id="admin-menu-button">
                            <i class="fas fa-user-shield mr-2"></i>
                            Admin
                            <i class="fas fa-chevron-down ml-2 transition-transform duration-200" id="dropdown-arrow"></i>
                        </button>

                        <!-- Admin Dropdown Menu -->
                        <div id="admin-dropdown"
                             class="hidden absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <div class="py-2">
                                <a href="{% url 'ordinances:admin_dashboard' %}"
                                   class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-200">
                                    <i class="fas fa-tachometer-alt mr-3 text-blue-500"></i>
                                    Admin Dashboard
                                </a>
                                <a href="{% url 'admin:index' %}"
                                   class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-200">
                                    <i class="fas fa-cogs mr-3 text-blue-500"></i>
                                    Django Admin
                                </a>
                                <hr class="my-2 border-gray-200">
                                <a href="{% url 'ordinances:admin_logout' %}"
                                   class="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200">
                                    <i class="fas fa-sign-out-alt mr-3"></i>
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Public Contact/Info Button -->
                <a href="#contact"
                   class="btn-primary px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200">
                    <i class="fas fa-phone mr-2"></i>
                    Contact
                </a>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden flex items-center">
                <button onclick="toggleMobileMenu()"
                        class="nav-link p-2 rounded-md"
                        id="mobile-menu-button">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="hidden md:hidden bg-primary-dark-blue">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <!-- Public Navigation Links -->
            <a href="{% url 'ordinances:home' %}"
               class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-home mr-2"></i>
                Home
            </a>
            <a href="{% url 'ordinances:ordinance_list' %}"
               class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-file-alt mr-2"></i>
                Ordinances
            </a>
            <a href="#contact"
               class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                <i class="fas fa-phone mr-2"></i>
                Contact
            </a>

            <!-- Admin Section - Only for Staff Users -->
            {% if user.is_authenticated and user.is_staff %}
                <div class="border-t border-primary-beige/20 mt-3 pt-3">
                    <div class="px-3 py-2 text-xs font-semibold text-primary-beige uppercase tracking-wider">
                        Admin Panel
                    </div>
                    <a href="{% url 'ordinances:admin_dashboard' %}"
                       class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Admin Dashboard
                    </a>
                    <a href="{% url 'admin:index' %}"
                       class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                        <i class="fas fa-cogs mr-3 text-blue-500"></i>
                        Django Admin
                    </a>
                    <a href="{% url 'ordinances:admin_logout' %}"
                       class="nav-link block px-3 py-2 rounded-md text-base font-medium text-red-300 hover:text-red-200 mt-2">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</nav>

<!-- Simple Dropdown JavaScript -->
<script>
// Simple dropdown functionality without Alpine.js dependency
let isAdminDropdownOpen = false;
let isMobileMenuOpen = false;

function toggleAdminDropdown() {
    const dropdown = document.getElementById('admin-dropdown');
    const arrow = document.getElementById('dropdown-arrow');

    if (!dropdown) return;

    isAdminDropdownOpen = !isAdminDropdownOpen;

    if (isAdminDropdownOpen) {
        dropdown.classList.remove('hidden');
        dropdown.classList.add('block');
        arrow.style.transform = 'rotate(180deg)';
    } else {
        dropdown.classList.add('hidden');
        dropdown.classList.remove('block');
        arrow.style.transform = 'rotate(0deg)';
    }
}

function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');

    if (!mobileMenu) return;

    isMobileMenuOpen = !isMobileMenuOpen;

    if (isMobileMenuOpen) {
        mobileMenu.classList.remove('hidden');
        mobileMenu.classList.add('block');
    } else {
        mobileMenu.classList.add('hidden');
        mobileMenu.classList.remove('block');
    }
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const adminButton = document.getElementById('admin-menu-button');
    const adminDropdown = document.getElementById('admin-dropdown');
    const mobileButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    // Close admin dropdown if clicking outside
    if (adminButton && adminDropdown && isAdminDropdownOpen) {
        if (!adminButton.contains(event.target) && !adminDropdown.contains(event.target)) {
            isAdminDropdownOpen = false;
            adminDropdown.classList.add('hidden');
            adminDropdown.classList.remove('block');
            document.getElementById('dropdown-arrow').style.transform = 'rotate(0deg)';
        }
    }

    // Close mobile menu if clicking outside
    if (mobileButton && mobileMenu && isMobileMenuOpen) {
        if (!mobileButton.contains(event.target) && !mobileMenu.contains(event.target)) {
            isMobileMenuOpen = false;
            mobileMenu.classList.add('hidden');
            mobileMenu.classList.remove('block');
        }
    }
});

// Close dropdowns on escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        if (isAdminDropdownOpen) {
            toggleAdminDropdown();
        }
        if (isMobileMenuOpen) {
            toggleMobileMenu();
        }
    }
});
</script>

<!-- Clean Navigation Styles -->
<style>
/* Navigation Base Styles */
.nav-primary {
    background: linear-gradient(135deg, var(--primary-dark-blue), var(--primary-black));
}

.nav-link {
    color: var(--primary-offwhite);
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-beige);
    background-color: rgba(241, 239, 236, 0.1);
}

.btn-primary {
    background-color: var(--primary-dark-blue);
    color: var(--primary-offwhite);
    border: 1px solid var(--primary-dark-blue);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-beige);
    color: var(--primary-dark-blue);
    border-color: var(--primary-beige);
}

/* Logo Hover Effects */
.group h1 {
    color: #F1EFEC;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 700;
}

.group p {
    color: #D4C9BE;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    font-weight: 500;
}

.group:hover h1 {
    color: #FFFFFF;
}

.group:hover p {
    color: #F1EFEC;
}

/* Dropdown Styles */
.dropdown-container {
    position: relative;
}

#admin-dropdown {
    position: absolute;
    right: 0;
    top: 100%;
    margin-top: 0.5rem;
    width: 14rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 50;
    overflow: hidden;
}

#admin-dropdown.block {
    display: block;
}

#admin-dropdown.hidden {
    display: none;
}

/* Dropdown Animation */
#admin-dropdown {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
    transition: all 0.2s ease-out;
}

#admin-dropdown.block {
    opacity: 1;
    transform: scale(1) translateY(0);
}

/* Dropdown Arrow Animation */
#dropdown-arrow {
    transition: transform 0.2s ease;
}

/* Mobile Menu Styles */
#mobile-menu {
    transition: all 0.3s ease;
}

#mobile-menu.block {
    display: block;
}

#mobile-menu.hidden {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-link:hover {
        transform: none;
        box-shadow: none;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Focus States for Accessibility */
button:focus,
a:focus {
    outline: 2px solid var(--primary-beige);
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .nav-link:hover {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid currentColor;
    }
}
</style>
